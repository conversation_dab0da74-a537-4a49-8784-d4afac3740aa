import React, { useEffect, useRef } from 'react'
import { Doughnut } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  ArcElement,
  Toolt<PERSON>,
  Legend,
} from 'chart.js'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { HiLightningBolt, HiTrendingDown, HiChartBar, HiTrendingUp } from 'react-icons/hi'

ChartJS.register(ArcElement, Tooltip, Legend)

function UsageDial() {
  const { state, usageSinceLastRecording, getDisplayUnitName } = useApp()
  const { theme } = useTheme()
  const chartRef = useRef(null)

  const remainingUnits = state.currentUnits
  const usedUnits = usageSinceLastRecording
  const totalUnits = remainingUnits + usedUnits
  const usagePercentage = totalUnits > 0 ? (usedUnits / totalUnits) * 100 : 0

  // Create advanced gradient colors with modern styling
  useEffect(() => {
    const chart = chartRef.current
    if (chart) {
      const ctx = chart.ctx

      // Advanced gradient for remaining units (vibrant blue to purple)
      const remainingGradient = ctx.createRadialGradient(200, 200, 50, 200, 200, 150)
      remainingGradient.addColorStop(0, '#667eea') // Light purple
      remainingGradient.addColorStop(0.3, '#764ba2') // Medium purple
      remainingGradient.addColorStop(0.6, '#667eea') // Light purple
      remainingGradient.addColorStop(1, '#f093fb') // Pink

      // Advanced gradient for used units (warm orange to red)
      const usedGradient = ctx.createRadialGradient(200, 200, 50, 200, 200, 150)
      usedGradient.addColorStop(0, '#ff9a9e') // Light coral
      usedGradient.addColorStop(0.3, '#fecfef') // Light pink
      usedGradient.addColorStop(0.6, '#fecfef') // Light pink
      usedGradient.addColorStop(1, '#ffc3a0') // Peach

      chart.data.datasets[0].backgroundColor = [remainingGradient, usedGradient]
      chart.update()
    }
  }, [remainingUnits, usedUnits])

  const data = {
    labels: [`Remaining ${getDisplayUnitName()}`, `Used ${getDisplayUnitName()}`],
    datasets: [
      {
        data: [remainingUnits, usedUnits],
        backgroundColor: [
          'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Modern gradient fallback
          'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)',
        ],
        borderColor: [
          'rgba(255, 255, 255, 0.9)',
          'rgba(255, 255, 255, 0.9)',
        ],
        borderWidth: 4,
        cutout: '78%',
        borderRadius: 12,
        borderJoinStyle: 'round',
        hoverBorderWidth: 6,
        hoverBorderColor: [
          'rgba(255, 255, 255, 1)',
          'rgba(255, 255, 255, 1)',
        ],
        shadowOffsetX: 3,
        shadowOffsetY: 3,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.1)',
      },
    ],
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // We'll create custom legend
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const label = context.label || ''
            const value = context.parsed
            const percentage = totalUnits > 0 ? ((value / totalUnits) * 100).toFixed(1) : 0
            return `${label}: ${value.toFixed(2)} (${percentage}%)`
          }
        }
      }
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeInOutCubic',
      delay: (context) => context.dataIndex * 200,
    },
    interaction: {
      intersect: false,
      mode: 'nearest',
    },
    elements: {
      arc: {
        borderWidth: 4,
        hoverBorderWidth: 6,
        borderSkipped: false,
        borderAlign: 'inner',
      }
    },
    layout: {
      padding: {
        top: 20,
        bottom: 20,
        left: 20,
        right: 20,
      }
    }
  }

  return (
    <div className="relative">
      {/* Modern Chart Container with Glassmorphism - Made Bigger */}
      <div className="relative h-[28rem] p-6">
        {/* Multi-layer background with modern gradients */}
        <div className={`absolute inset-0 ${theme.secondary} rounded-3xl opacity-60`}></div>
        <div className={`absolute inset-1 ${theme.card} rounded-2xl backdrop-blur-lg shadow-2xl`}></div>
        <div className={`absolute inset-3 ${theme.secondary} to-transparent rounded-xl`}></div>

        {/* Floating orbs for ambient effect */}
        <div className={`absolute top-4 left-4 w-16 h-16 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-xl animate-pulse`}></div>
        <div className={`absolute bottom-4 right-4 w-20 h-20 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-2xl animate-pulse`} style={{animationDelay: '1s'}}></div>

        {/* Chart - Made Bigger */}
        <div className="relative h-full flex items-center justify-center">
          <div className="w-full h-full max-w-md max-h-md">
            <Doughnut ref={chartRef} data={data} options={options} />
          </div>

          {/* Enhanced Center Content with Circular Design - Made Bigger */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative">
              {/* Outer glow ring - Made Bigger */}
              <div className="absolute inset-0 w-48 h-48 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>

              {/* Main circular container - Made Bigger */}
              <div className={`relative w-48 h-48 ${theme.card}/40 border-2 ${theme.border}/30 backdrop-blur-lg rounded-full shadow-2xl flex items-center justify-center`}>
                {/* Inner gradient ring */}
                <div className={`absolute inset-2 ${theme.secondary}/60 rounded-full`}></div>

                {/* Content container */}
                <div className="relative text-center z-10">
                  {/* Animated icon with glow effect - Made Bigger */}
                  <div className="mb-3 flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"></div>
                      <HiLightningBolt className="relative h-10 w-10 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce" style={{animationDuration: '2s'}} />
                    </div>
                  </div>

                  {/* Main value with enhanced gradient text - Made Bigger */}
                  <div className="relative mb-2">
                    <div className={`text-4xl font-black ${theme.text} drop-shadow-lg`}>
                      {remainingUnits.toFixed(2)}
                    </div>
                    <div className={`text-sm font-semibold ${theme.textSecondary} mt-1 tracking-wide`}>
                      {getDisplayUnitName()} Left
                    </div>
                  </div>

                  {/* Usage percentage with enhanced styling */}
                  <div className="mt-2">
                    <div className={`text-base font-bold tracking-tight ${theme.textSecondary} drop-shadow-lg`}>
                      {usagePercentage.toFixed(1)}% Used
                    </div>
                  </div>
                </div>

                {/* Decorative inner elements */}
                <div className="absolute top-4 right-4 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-pulse"></div>
                <div className="absolute bottom-4 left-4 w-1.5 h-1.5 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1s'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Units and Usage Since Last Recording Cards */}
      <div className="mt-8 space-y-4">
        {/* Current Units Card */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold mb-2 ${theme.text}`}>Current Units</h3>
              <p className={`text-3xl font-bold ${theme.text}`}>
                {state.currentUnits.toFixed(2)}
              </p>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>{getDisplayUnitName()} remaining</p>
              <p className={`text-lg ${theme.textSecondary} font-semibold mt-2`}>
                Value: {state.currencySymbol}{(state.currentUnits * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className={`p-4 rounded-2xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
              <HiLightningBolt className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        {/* Usage Since Last Recording Card */}
        <div className={`${theme.card} rounded-2xl shadow-lg p-6 border ${theme.border}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold mb-2 ${theme.text}`}>Usage Since Last Recording</h3>
              <p className={`text-3xl font-bold ${theme.text}`}>
                {usageSinceLastRecording.toFixed(2)}
              </p>
              <p className={`text-sm font-medium ${theme.textSecondary}`}>{getDisplayUnitName()} used</p>
              <p className={`text-lg ${theme.textSecondary} font-semibold mt-2`}>
                Cost: {state.currencySymbol}{(usageSinceLastRecording * state.unitCost).toFixed(2)}
              </p>
            </div>
            <div className={`p-4 rounded-2xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
              <HiTrendingUp className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Modern Cost Summary with Glassmorphism */}
      <div className={`mt-8 relative overflow-hidden rounded-2xl ${theme.card} border ${theme.border} p-6 shadow-xl`}>
        <div className={`absolute inset-0 ${theme.secondary}`}></div>
        <div className="relative flex justify-between items-center">
          <div className="space-y-2">
            <div className={`text-sm font-semibold ${theme.textSecondary} tracking-wider uppercase`}>Total Cost</div>
            <div className={`text-3xl font-black ${theme.text}`}>
              {state.currencySymbol || 'R'}{(usedUnits * state.unitCost).toFixed(2)}
            </div>
          </div>
          <div className="text-right space-y-2">
            <div className={`text-sm font-semibold ${theme.textSecondary} tracking-wider uppercase`}>Rate</div>
            <div className={`text-xl font-bold ${theme.text}`}>
              {state.currencySymbol || 'R'}{state.unitCost.toFixed(2)}/{getDisplayUnitName()}
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className={`absolute top-2 right-2 w-8 h-8 bg-gradient-to-r ${theme.gradient} rounded-full opacity-20 blur-sm`}></div>
        <div className={`absolute bottom-2 left-2 w-6 h-6 bg-gradient-to-r ${theme.gradient} rounded-full opacity-15 blur-sm`}></div>
      </div>

      {/* Modern Progress Bar with Enhanced Styling */}
      <div className="mt-8">
        <div className="flex justify-between items-center mb-4">
          <span className={`text-sm font-bold ${theme.textSecondary} tracking-wide uppercase`}>Usage Progress</span>
          <span className={`text-lg font-black px-3 py-1 rounded-full ${theme.secondary} ${theme.text}`}>
            {usagePercentage.toFixed(1)}%
          </span>
        </div>
        <div className="relative">
          {/* Background track */}
          <div className={`w-full ${theme.secondary} rounded-full h-4 border ${theme.border}`}>
            {/* Progress fill - clean and crisp */}
            <div
              className={`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${
                usagePercentage > 70 ? 'from-red-500 to-red-600' :
                usagePercentage > 40 ? 'from-amber-500 to-orange-600' :
                'from-emerald-500 to-green-600'
              }`}
              style={{
                width: `${Math.min(usagePercentage, 100)}%`
              }}
            />
          </div>


        </div>
      </div>
    </div>
  )
}

export default UsageDial
