@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

/* Custom animations */
@keyframes lightning {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

.lightning-animation {
  animation: lightning 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Remove spinner arrows from number inputs */
/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Prevent scroll wheel from changing number input values */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Additional protection against scroll events */
input[type="number"]:focus {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Mobile safe area support */
@supports (padding: env(safe-area-inset-top)) {
  .pt-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .pb-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pb-safe-bottom-nav {
    padding-bottom: calc(env(safe-area-inset-bottom) + 5rem); /* 5rem = 80px for nav height */
  }
}

/* Fallback for devices without safe area support */
@supports not (padding: env(safe-area-inset-top)) {
  .pt-safe-top {
    padding-top: 1rem; /* 16px fallback */
  }

  .pb-safe-bottom {
    padding-bottom: 0.5rem; /* 8px fallback */
  }

  .pb-safe-bottom-nav {
    padding-bottom: 5rem; /* 80px for nav height */
  }
}
