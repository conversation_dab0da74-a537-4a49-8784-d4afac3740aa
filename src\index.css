@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

/* Custom animations */
@keyframes lightning {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

.lightning-animation {
  animation: lightning 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Remove spinner arrows from number inputs */
/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Prevent scroll wheel from changing number input values */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Additional protection against scroll events */
input[type="number"]:focus {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Mobile safe area support - Fixed approach */
.safe-top {
  padding-top: env(safe-area-inset-top, 0px);
  padding-top: calc(env(safe-area-inset-top, 0px) + 1rem);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0px);
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.5rem);
}

.safe-bottom-nav {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 5rem);
}

/* Mobile-specific adjustments with proper viewport support */
@media (max-width: 768px) {
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top, 0px);
    padding-top: calc(env(safe-area-inset-top, 0px) + 1.5rem);
  }

  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom, 0px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.75rem);
  }

  .mobile-safe-bottom-nav {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 5rem);
  }

  /* Ensure proper header spacing */
  .mobile-header {
    min-height: calc(4rem + env(safe-area-inset-top, 0px));
    padding-top: calc(env(safe-area-inset-top, 0px) + 1rem);
  }

  /* Ensure proper footer spacing */
  .mobile-footer {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.5rem);
    min-height: calc(5rem + env(safe-area-inset-bottom, 0px));
  }
}

/* Additional viewport height fixes for mobile */
@media (max-width: 768px) and (orientation: portrait) {
  html, body {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  #root {
    height: 100vh;
    height: 100dvh;
  }
}
