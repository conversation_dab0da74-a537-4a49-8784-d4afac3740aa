import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom'
import { AppProvider } from './context/AppContext'
import { ThemeProvider } from './context/ThemeContext'
import Layout from './components/Layout/Layout'
import { useAndroidBackButton } from './hooks/useAndroidBackButton'

// Component that uses the back button hook
function AppContent() {
  useAndroidBackButton();
  return <Layout />;
}

function App() {
  return (
    <Router>
      <ThemeProvider>
        <AppProvider>
          <AppContent />
        </AppProvider>
      </ThemeProvider>
    </Router>
  )
}

export default App
