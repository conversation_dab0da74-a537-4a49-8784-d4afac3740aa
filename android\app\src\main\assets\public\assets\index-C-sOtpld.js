var Pg=Object.defineProperty;var Mg=(t,e,n)=>e in t?Pg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var F=(t,e,n)=>Mg(t,typeof e!="symbol"?e+"":e,n);function $g(t,e){for(var n=0;n<e.length;n++){const r=e[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in t)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(t,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function Eg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var ef={exports:{}},Aa={},tf={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ki=Symbol.for("react.element"),Tg=Symbol.for("react.portal"),Rg=Symbol.for("react.fragment"),Dg=Symbol.for("react.strict_mode"),Og=Symbol.for("react.profiler"),Lg=Symbol.for("react.provider"),zg=Symbol.for("react.context"),Fg=Symbol.for("react.forward_ref"),Ag=Symbol.for("react.suspense"),Ig=Symbol.for("react.memo"),Ug=Symbol.for("react.lazy"),gu=Symbol.iterator;function Hg(t){return t===null||typeof t!="object"?null:(t=gu&&t[gu]||t["@@iterator"],typeof t=="function"?t:null)}var nf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},rf=Object.assign,sf={};function Or(t,e,n){this.props=t,this.context=e,this.refs=sf,this.updater=n||nf}Or.prototype.isReactComponent={};Or.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Or.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function af(){}af.prototype=Or.prototype;function sc(t,e,n){this.props=t,this.context=e,this.refs=sf,this.updater=n||nf}var ac=sc.prototype=new af;ac.constructor=sc;rf(ac,Or.prototype);ac.isPureReactComponent=!0;var xu=Array.isArray,of=Object.prototype.hasOwnProperty,oc={current:null},lf={key:!0,ref:!0,__self:!0,__source:!0};function cf(t,e,n){var r,i={},s=null,a=null;if(e!=null)for(r in e.ref!==void 0&&(a=e.ref),e.key!==void 0&&(s=""+e.key),e)of.call(e,r)&&!lf.hasOwnProperty(r)&&(i[r]=e[r]);var o=arguments.length-2;if(o===1)i.children=n;else if(1<o){for(var l=Array(o),u=0;u<o;u++)l[u]=arguments[u+2];i.children=l}if(t&&t.defaultProps)for(r in o=t.defaultProps,o)i[r]===void 0&&(i[r]=o[r]);return{$$typeof:Ki,type:t,key:s,ref:a,props:i,_owner:oc.current}}function Bg(t,e){return{$$typeof:Ki,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function lc(t){return typeof t=="object"&&t!==null&&t.$$typeof===Ki}function Wg(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var yu=/\/+/g;function co(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Wg(""+t.key):e.toString(36)}function Os(t,e,n,r,i){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(s){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case Ki:case Tg:a=!0}}if(a)return a=t,i=i(a),t=r===""?"."+co(a,0):r,xu(i)?(n="",t!=null&&(n=t.replace(yu,"$&/")+"/"),Os(i,e,n,"",function(u){return u})):i!=null&&(lc(i)&&(i=Bg(i,n+(!i.key||a&&a.key===i.key?"":(""+i.key).replace(yu,"$&/")+"/")+t)),e.push(i)),1;if(a=0,r=r===""?".":r+":",xu(t))for(var o=0;o<t.length;o++){s=t[o];var l=r+co(s,o);a+=Os(s,e,n,l,i)}else if(l=Hg(t),typeof l=="function")for(t=l.call(t),o=0;!(s=t.next()).done;)s=s.value,l=r+co(s,o++),a+=Os(s,e,n,l,i);else if(s==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return a}function is(t,e,n){if(t==null)return t;var r=[],i=0;return Os(t,r,"","",function(s){return e.call(n,s,i++)}),r}function Vg(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var Oe={current:null},Ls={transition:null},Yg={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:Ls,ReactCurrentOwner:oc};function uf(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:is,forEach:function(t,e,n){is(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return is(t,function(){e++}),e},toArray:function(t){return is(t,function(e){return e})||[]},only:function(t){if(!lc(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};A.Component=Or;A.Fragment=Rg;A.Profiler=Og;A.PureComponent=sc;A.StrictMode=Dg;A.Suspense=Ag;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yg;A.act=uf;A.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=rf({},t.props),i=t.key,s=t.ref,a=t._owner;if(e!=null){if(e.ref!==void 0&&(s=e.ref,a=oc.current),e.key!==void 0&&(i=""+e.key),t.type&&t.type.defaultProps)var o=t.type.defaultProps;for(l in e)of.call(e,l)&&!lf.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&o!==void 0?o[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){o=Array(l);for(var u=0;u<l;u++)o[u]=arguments[u+2];r.children=o}return{$$typeof:Ki,type:t.type,key:i,ref:s,props:r,_owner:a}};A.createContext=function(t){return t={$$typeof:zg,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:Lg,_context:t},t.Consumer=t};A.createElement=cf;A.createFactory=function(t){var e=cf.bind(null,t);return e.type=t,e};A.createRef=function(){return{current:null}};A.forwardRef=function(t){return{$$typeof:Fg,render:t}};A.isValidElement=lc;A.lazy=function(t){return{$$typeof:Ug,_payload:{_status:-1,_result:t},_init:Vg}};A.memo=function(t,e){return{$$typeof:Ig,type:t,compare:e===void 0?null:e}};A.startTransition=function(t){var e=Ls.transition;Ls.transition={};try{t()}finally{Ls.transition=e}};A.unstable_act=uf;A.useCallback=function(t,e){return Oe.current.useCallback(t,e)};A.useContext=function(t){return Oe.current.useContext(t)};A.useDebugValue=function(){};A.useDeferredValue=function(t){return Oe.current.useDeferredValue(t)};A.useEffect=function(t,e){return Oe.current.useEffect(t,e)};A.useId=function(){return Oe.current.useId()};A.useImperativeHandle=function(t,e,n){return Oe.current.useImperativeHandle(t,e,n)};A.useInsertionEffect=function(t,e){return Oe.current.useInsertionEffect(t,e)};A.useLayoutEffect=function(t,e){return Oe.current.useLayoutEffect(t,e)};A.useMemo=function(t,e){return Oe.current.useMemo(t,e)};A.useReducer=function(t,e,n){return Oe.current.useReducer(t,e,n)};A.useRef=function(t){return Oe.current.useRef(t)};A.useState=function(t){return Oe.current.useState(t)};A.useSyncExternalStore=function(t,e,n){return Oe.current.useSyncExternalStore(t,e,n)};A.useTransition=function(){return Oe.current.useTransition()};A.version="18.3.1";tf.exports=A;var k=tf.exports;const gt=Eg(k),Xg=$g({__proto__:null,default:gt},[k]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qg=k,Kg=Symbol.for("react.element"),Gg=Symbol.for("react.fragment"),qg=Object.prototype.hasOwnProperty,Zg=Qg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Jg={key:!0,ref:!0,__self:!0,__source:!0};function df(t,e,n){var r,i={},s=null,a=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(a=e.ref);for(r in e)qg.call(e,r)&&!Jg.hasOwnProperty(r)&&(i[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)i[r]===void 0&&(i[r]=e[r]);return{$$typeof:Kg,type:t,key:s,ref:a,props:i,_owner:Zg.current}}Aa.Fragment=Gg;Aa.jsx=df;Aa.jsxs=df;ef.exports=Aa;var c=ef.exports,Go={},hf={exports:{}},Ze={},ff={exports:{}},mf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(P,O){var z=P.length;P.push(O);e:for(;0<z;){var K=z-1>>>1,G=P[K];if(0<i(G,O))P[K]=O,P[z]=G,z=K;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var O=P[0],z=P.pop();if(z!==O){P[0]=z;e:for(var K=0,G=P.length,bt=G>>>1;K<bt;){var $e=2*(K+1)-1,$t=P[$e],Ee=$e+1,rs=P[Ee];if(0>i($t,z))Ee<G&&0>i(rs,$t)?(P[K]=rs,P[Ee]=z,K=Ee):(P[K]=$t,P[$e]=z,K=$e);else if(Ee<G&&0>i(rs,z))P[K]=rs,P[Ee]=z,K=Ee;else break e}}return O}function i(P,O){var z=P.sortIndex-O.sortIndex;return z!==0?z:P.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var a=Date,o=a.now();t.unstable_now=function(){return a.now()-o}}var l=[],u=[],d=1,h=null,f=3,m=!1,x=!1,y=!1,b=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(P){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=P)r(u),O.sortIndex=O.expirationTime,e(l,O);else break;O=n(u)}}function w(P){if(y=!1,v(P),!x)if(n(l)!==null)x=!0,U(_);else{var O=n(u);O!==null&&T(w,O.startTime-P)}}function _(P,O){x=!1,y&&(y=!1,g(j),j=-1),m=!0;var z=f;try{for(v(O),h=n(l);h!==null&&(!(h.expirationTime>O)||P&&!E());){var K=h.callback;if(typeof K=="function"){h.callback=null,f=h.priorityLevel;var G=K(h.expirationTime<=O);O=t.unstable_now(),typeof G=="function"?h.callback=G:h===n(l)&&r(l),v(O)}else r(l);h=n(l)}if(h!==null)var bt=!0;else{var $e=n(u);$e!==null&&T(w,$e.startTime-O),bt=!1}return bt}finally{h=null,f=z,m=!1}}var S=!1,N=null,j=-1,$=5,M=-1;function E(){return!(t.unstable_now()-M<$)}function L(){if(N!==null){var P=t.unstable_now();M=P;var O=!0;try{O=N(!0,P)}finally{O?R():(S=!1,N=null)}}else S=!1}var R;if(typeof p=="function")R=function(){p(L)};else if(typeof MessageChannel<"u"){var V=new MessageChannel,I=V.port2;V.port1.onmessage=L,R=function(){I.postMessage(null)}}else R=function(){b(L,0)};function U(P){N=P,S||(S=!0,R())}function T(P,O){j=b(function(){P(t.unstable_now())},O)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(P){P.callback=null},t.unstable_continueExecution=function(){x||m||(x=!0,U(_))},t.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<P?Math.floor(1e3/P):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_getFirstCallbackNode=function(){return n(l)},t.unstable_next=function(P){switch(f){case 1:case 2:case 3:var O=3;break;default:O=f}var z=f;f=O;try{return P()}finally{f=z}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(P,O){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var z=f;f=P;try{return O()}finally{f=z}},t.unstable_scheduleCallback=function(P,O,z){var K=t.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?K+z:K):z=K,P){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=z+G,P={id:d++,callback:O,priorityLevel:P,startTime:z,expirationTime:G,sortIndex:-1},z>K?(P.sortIndex=z,e(u,P),n(l)===null&&P===n(u)&&(y?(g(j),j=-1):y=!0,T(w,z-K))):(P.sortIndex=G,e(l,P),x||m||(x=!0,U(_))),P},t.unstable_shouldYield=E,t.unstable_wrapCallback=function(P){var O=f;return function(){var z=f;f=O;try{return P.apply(this,arguments)}finally{f=z}}}})(mf);ff.exports=mf;var e0=ff.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t0=k,qe=e0;function C(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var pf=new Set,Ni={};function Gn(t,e){kr(t,e),kr(t+"Capture",e)}function kr(t,e){for(Ni[t]=e,t=0;t<e.length;t++)pf.add(e[t])}var Ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),qo=Object.prototype.hasOwnProperty,n0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,vu={},bu={};function r0(t){return qo.call(bu,t)?!0:qo.call(vu,t)?!1:n0.test(t)?bu[t]=!0:(vu[t]=!0,!1)}function i0(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function s0(t,e,n,r){if(e===null||typeof e>"u"||i0(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function Le(t,e,n,r,i,s,a){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=s,this.removeEmptyString=a}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){_e[t]=new Le(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];_e[e]=new Le(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){_e[t]=new Le(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){_e[t]=new Le(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){_e[t]=new Le(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){_e[t]=new Le(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){_e[t]=new Le(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){_e[t]=new Le(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){_e[t]=new Le(t,5,!1,t.toLowerCase(),null,!1,!1)});var cc=/[\-:]([a-z])/g;function uc(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(cc,uc);_e[e]=new Le(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(cc,uc);_e[e]=new Le(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(cc,uc);_e[e]=new Le(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){_e[t]=new Le(t,1,!1,t.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){_e[t]=new Le(t,1,!1,t.toLowerCase(),null,!0,!0)});function dc(t,e,n,r){var i=_e.hasOwnProperty(e)?_e[e]:null;(i!==null?i.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(s0(e,n,i,r)&&(n=null),r||i===null?r0(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=n===null?i.type===3?!1:"":n:(e=i.attributeName,r=i.attributeNamespace,n===null?t.removeAttribute(e):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var Yt=t0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ss=Symbol.for("react.element"),rr=Symbol.for("react.portal"),ir=Symbol.for("react.fragment"),hc=Symbol.for("react.strict_mode"),Zo=Symbol.for("react.profiler"),gf=Symbol.for("react.provider"),xf=Symbol.for("react.context"),fc=Symbol.for("react.forward_ref"),Jo=Symbol.for("react.suspense"),el=Symbol.for("react.suspense_list"),mc=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),yf=Symbol.for("react.offscreen"),wu=Symbol.iterator;function Hr(t){return t===null||typeof t!="object"?null:(t=wu&&t[wu]||t["@@iterator"],typeof t=="function"?t:null)}var ae=Object.assign,uo;function ri(t){if(uo===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);uo=e&&e[1]||""}return`
`+uo+t}var ho=!1;function fo(t,e){if(!t||ho)return"";ho=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),a=i.length-1,o=s.length-1;1<=a&&0<=o&&i[a]!==s[o];)o--;for(;1<=a&&0<=o;a--,o--)if(i[a]!==s[o]){if(a!==1||o!==1)do if(a--,o--,0>o||i[a]!==s[o]){var l=`
`+i[a].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=a&&0<=o);break}}}finally{ho=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?ri(t):""}function a0(t){switch(t.tag){case 5:return ri(t.type);case 16:return ri("Lazy");case 13:return ri("Suspense");case 19:return ri("SuspenseList");case 0:case 2:case 15:return t=fo(t.type,!1),t;case 11:return t=fo(t.type.render,!1),t;case 1:return t=fo(t.type,!0),t;default:return""}}function tl(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case ir:return"Fragment";case rr:return"Portal";case Zo:return"Profiler";case hc:return"StrictMode";case Jo:return"Suspense";case el:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case xf:return(t.displayName||"Context")+".Consumer";case gf:return(t._context.displayName||"Context")+".Provider";case fc:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case mc:return e=t.displayName||null,e!==null?e:tl(t.type)||"Memo";case Gt:e=t._payload,t=t._init;try{return tl(t(e))}catch{}}return null}function o0(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return tl(e);case 8:return e===hc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function yn(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function vf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function l0(t){var e=vf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){r=""+a,s.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(a){r=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function as(t){t._valueTracker||(t._valueTracker=l0(t))}function bf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=vf(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function Zs(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function nl(t,e){var n=e.checked;return ae({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??t._wrapperState.initialChecked})}function Su(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=yn(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function wf(t,e){e=e.checked,e!=null&&dc(t,"checked",e,!1)}function rl(t,e){wf(t,e);var n=yn(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?il(t,e.type,n):e.hasOwnProperty("defaultValue")&&il(t,e.type,yn(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function _u(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function il(t,e,n){(e!=="number"||Zs(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var ii=Array.isArray;function pr(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+yn(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,r&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function sl(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(C(91));return ae({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Nu(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(C(92));if(ii(n)){if(1<n.length)throw Error(C(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:yn(n)}}function Sf(t,e){var n=yn(e.value),r=yn(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function ku(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function _f(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function al(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?_f(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var os,Nf=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(e,n,r,i){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,i)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(os=os||document.createElement("div"),os.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=os.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function ki(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var hi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},c0=["Webkit","ms","Moz","O"];Object.keys(hi).forEach(function(t){c0.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),hi[e]=hi[t]})});function kf(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||hi.hasOwnProperty(t)&&hi[t]?(""+e).trim():e+"px"}function jf(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=kf(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}var u0=ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ol(t,e){if(e){if(u0[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(C(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(C(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(C(61))}if(e.style!=null&&typeof e.style!="object")throw Error(C(62))}}function ll(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cl=null;function pc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ul=null,gr=null,xr=null;function ju(t){if(t=Zi(t)){if(typeof ul!="function")throw Error(C(280));var e=t.stateNode;e&&(e=Wa(e),ul(t.stateNode,t.type,e))}}function Cf(t){gr?xr?xr.push(t):xr=[t]:gr=t}function Pf(){if(gr){var t=gr,e=xr;if(xr=gr=null,ju(t),e)for(t=0;t<e.length;t++)ju(e[t])}}function Mf(t,e){return t(e)}function $f(){}var mo=!1;function Ef(t,e,n){if(mo)return t(e,n);mo=!0;try{return Mf(t,e,n)}finally{mo=!1,(gr!==null||xr!==null)&&($f(),Pf())}}function ji(t,e){var n=t.stateNode;if(n===null)return null;var r=Wa(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(C(231,e,typeof n));return n}var dl=!1;if(Ut)try{var Br={};Object.defineProperty(Br,"passive",{get:function(){dl=!0}}),window.addEventListener("test",Br,Br),window.removeEventListener("test",Br,Br)}catch{dl=!1}function d0(t,e,n,r,i,s,a,o,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var fi=!1,Js=null,ea=!1,hl=null,h0={onError:function(t){fi=!0,Js=t}};function f0(t,e,n,r,i,s,a,o,l){fi=!1,Js=null,d0.apply(h0,arguments)}function m0(t,e,n,r,i,s,a,o,l){if(f0.apply(this,arguments),fi){if(fi){var u=Js;fi=!1,Js=null}else throw Error(C(198));ea||(ea=!0,hl=u)}}function qn(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Tf(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Cu(t){if(qn(t)!==t)throw Error(C(188))}function p0(t){var e=t.alternate;if(!e){if(e=qn(t),e===null)throw Error(C(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Cu(i),t;if(s===r)return Cu(i),e;s=s.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=s;else{for(var a=!1,o=i.child;o;){if(o===n){a=!0,n=i,r=s;break}if(o===r){a=!0,r=i,n=s;break}o=o.sibling}if(!a){for(o=s.child;o;){if(o===n){a=!0,n=s,r=i;break}if(o===r){a=!0,r=s,n=i;break}o=o.sibling}if(!a)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?t:e}function Rf(t){return t=p0(t),t!==null?Df(t):null}function Df(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=Df(t);if(e!==null)return e;t=t.sibling}return null}var Of=qe.unstable_scheduleCallback,Pu=qe.unstable_cancelCallback,g0=qe.unstable_shouldYield,x0=qe.unstable_requestPaint,ce=qe.unstable_now,y0=qe.unstable_getCurrentPriorityLevel,gc=qe.unstable_ImmediatePriority,Lf=qe.unstable_UserBlockingPriority,ta=qe.unstable_NormalPriority,v0=qe.unstable_LowPriority,zf=qe.unstable_IdlePriority,Ia=null,Ct=null;function b0(t){if(Ct&&typeof Ct.onCommitFiberRoot=="function")try{Ct.onCommitFiberRoot(Ia,t,void 0,(t.current.flags&128)===128)}catch{}}var xt=Math.clz32?Math.clz32:_0,w0=Math.log,S0=Math.LN2;function _0(t){return t>>>=0,t===0?32:31-(w0(t)/S0|0)|0}var ls=64,cs=4194304;function si(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function na(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,i=t.suspendedLanes,s=t.pingedLanes,a=n&268435455;if(a!==0){var o=a&~i;o!==0?r=si(o):(s&=a,s!==0&&(r=si(s)))}else a=n&~i,a!==0?r=si(a):s!==0&&(r=si(s));if(r===0)return 0;if(e!==0&&e!==r&&!(e&i)&&(i=r&-r,s=e&-e,i>=s||i===16&&(s&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-xt(e),i=1<<n,r|=t[n],e&=~i;return r}function N0(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function k0(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,i=t.expirationTimes,s=t.pendingLanes;0<s;){var a=31-xt(s),o=1<<a,l=i[a];l===-1?(!(o&n)||o&r)&&(i[a]=N0(o,e)):l<=e&&(t.expiredLanes|=o),s&=~o}}function fl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Ff(){var t=ls;return ls<<=1,!(ls&4194240)&&(ls=64),t}function po(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Gi(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-xt(e),t[e]=n}function j0(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var i=31-xt(n),s=1<<i;e[i]=0,r[i]=-1,t[i]=-1,n&=~s}}function xc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-xt(n),i=1<<r;i&e|t[r]&e&&(t[r]|=e),n&=~i}}var Q=0;function Af(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var If,yc,Uf,Hf,Bf,ml=!1,us=[],an=null,on=null,ln=null,Ci=new Map,Pi=new Map,Zt=[],C0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mu(t,e){switch(t){case"focusin":case"focusout":an=null;break;case"dragenter":case"dragleave":on=null;break;case"mouseover":case"mouseout":ln=null;break;case"pointerover":case"pointerout":Ci.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pi.delete(e.pointerId)}}function Wr(t,e,n,r,i,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},e!==null&&(e=Zi(e),e!==null&&yc(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function P0(t,e,n,r,i){switch(e){case"focusin":return an=Wr(an,t,e,n,r,i),!0;case"dragenter":return on=Wr(on,t,e,n,r,i),!0;case"mouseover":return ln=Wr(ln,t,e,n,r,i),!0;case"pointerover":var s=i.pointerId;return Ci.set(s,Wr(Ci.get(s)||null,t,e,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Pi.set(s,Wr(Pi.get(s)||null,t,e,n,r,i)),!0}return!1}function Wf(t){var e=On(t.target);if(e!==null){var n=qn(e);if(n!==null){if(e=n.tag,e===13){if(e=Tf(n),e!==null){t.blockedOn=e,Bf(t.priority,function(){Uf(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=pl(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);cl=r,n.target.dispatchEvent(r),cl=null}else return e=Zi(n),e!==null&&yc(e),t.blockedOn=n,!1;e.shift()}return!0}function $u(t,e,n){zs(t)&&n.delete(e)}function M0(){ml=!1,an!==null&&zs(an)&&(an=null),on!==null&&zs(on)&&(on=null),ln!==null&&zs(ln)&&(ln=null),Ci.forEach($u),Pi.forEach($u)}function Vr(t,e){t.blockedOn===e&&(t.blockedOn=null,ml||(ml=!0,qe.unstable_scheduleCallback(qe.unstable_NormalPriority,M0)))}function Mi(t){function e(i){return Vr(i,t)}if(0<us.length){Vr(us[0],t);for(var n=1;n<us.length;n++){var r=us[n];r.blockedOn===t&&(r.blockedOn=null)}}for(an!==null&&Vr(an,t),on!==null&&Vr(on,t),ln!==null&&Vr(ln,t),Ci.forEach(e),Pi.forEach(e),n=0;n<Zt.length;n++)r=Zt[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Zt.length&&(n=Zt[0],n.blockedOn===null);)Wf(n),n.blockedOn===null&&Zt.shift()}var yr=Yt.ReactCurrentBatchConfig,ra=!0;function $0(t,e,n,r){var i=Q,s=yr.transition;yr.transition=null;try{Q=1,vc(t,e,n,r)}finally{Q=i,yr.transition=s}}function E0(t,e,n,r){var i=Q,s=yr.transition;yr.transition=null;try{Q=4,vc(t,e,n,r)}finally{Q=i,yr.transition=s}}function vc(t,e,n,r){if(ra){var i=pl(t,e,n,r);if(i===null)ko(t,e,r,ia,n),Mu(t,r);else if(P0(i,t,e,n,r))r.stopPropagation();else if(Mu(t,r),e&4&&-1<C0.indexOf(t)){for(;i!==null;){var s=Zi(i);if(s!==null&&If(s),s=pl(t,e,n,r),s===null&&ko(t,e,r,ia,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else ko(t,e,r,null,n)}}var ia=null;function pl(t,e,n,r){if(ia=null,t=pc(r),t=On(t),t!==null)if(e=qn(t),e===null)t=null;else if(n=e.tag,n===13){if(t=Tf(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return ia=t,null}function Vf(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(y0()){case gc:return 1;case Lf:return 4;case ta:case v0:return 16;case zf:return 536870912;default:return 16}default:return 16}}var en=null,bc=null,Fs=null;function Yf(){if(Fs)return Fs;var t,e=bc,n=e.length,r,i="value"in en?en.value:en.textContent,s=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(r=1;r<=a&&e[n-r]===i[s-r];r++);return Fs=i.slice(t,1<r?1-r:void 0)}function As(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ds(){return!0}function Eu(){return!1}function Je(t){function e(n,r,i,s,a){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=a,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(n=t[o],this[o]=n?n(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ds:Eu,this.isPropagationStopped=Eu,this}return ae(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ds)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ds)},persist:function(){},isPersistent:ds}),e}var Lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wc=Je(Lr),qi=ae({},Lr,{view:0,detail:0}),T0=Je(qi),go,xo,Yr,Ua=ae({},qi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Sc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Yr&&(Yr&&t.type==="mousemove"?(go=t.screenX-Yr.screenX,xo=t.screenY-Yr.screenY):xo=go=0,Yr=t),go)},movementY:function(t){return"movementY"in t?t.movementY:xo}}),Tu=Je(Ua),R0=ae({},Ua,{dataTransfer:0}),D0=Je(R0),O0=ae({},qi,{relatedTarget:0}),yo=Je(O0),L0=ae({},Lr,{animationName:0,elapsedTime:0,pseudoElement:0}),z0=Je(L0),F0=ae({},Lr,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),A0=Je(F0),I0=ae({},Lr,{data:0}),Ru=Je(I0),U0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},H0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},B0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function W0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=B0[t])?!!e[t]:!1}function Sc(){return W0}var V0=ae({},qi,{key:function(t){if(t.key){var e=U0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=As(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?H0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Sc,charCode:function(t){return t.type==="keypress"?As(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?As(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Y0=Je(V0),X0=ae({},Ua,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Du=Je(X0),Q0=ae({},qi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Sc}),K0=Je(Q0),G0=ae({},Lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),q0=Je(G0),Z0=ae({},Ua,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),J0=Je(Z0),ex=[9,13,27,32],_c=Ut&&"CompositionEvent"in window,mi=null;Ut&&"documentMode"in document&&(mi=document.documentMode);var tx=Ut&&"TextEvent"in window&&!mi,Xf=Ut&&(!_c||mi&&8<mi&&11>=mi),Ou=" ",Lu=!1;function Qf(t,e){switch(t){case"keyup":return ex.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Kf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var sr=!1;function nx(t,e){switch(t){case"compositionend":return Kf(e);case"keypress":return e.which!==32?null:(Lu=!0,Ou);case"textInput":return t=e.data,t===Ou&&Lu?null:t;default:return null}}function rx(t,e){if(sr)return t==="compositionend"||!_c&&Qf(t,e)?(t=Yf(),Fs=bc=en=null,sr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Xf&&e.locale!=="ko"?null:e.data;default:return null}}var ix={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!ix[t.type]:e==="textarea"}function Gf(t,e,n,r){Cf(r),e=sa(e,"onChange"),0<e.length&&(n=new wc("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var pi=null,$i=null;function sx(t){om(t,0)}function Ha(t){var e=lr(t);if(bf(e))return t}function ax(t,e){if(t==="change")return e}var qf=!1;if(Ut){var vo;if(Ut){var bo="oninput"in document;if(!bo){var Fu=document.createElement("div");Fu.setAttribute("oninput","return;"),bo=typeof Fu.oninput=="function"}vo=bo}else vo=!1;qf=vo&&(!document.documentMode||9<document.documentMode)}function Au(){pi&&(pi.detachEvent("onpropertychange",Zf),$i=pi=null)}function Zf(t){if(t.propertyName==="value"&&Ha($i)){var e=[];Gf(e,$i,t,pc(t)),Ef(sx,e)}}function ox(t,e,n){t==="focusin"?(Au(),pi=e,$i=n,pi.attachEvent("onpropertychange",Zf)):t==="focusout"&&Au()}function lx(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ha($i)}function cx(t,e){if(t==="click")return Ha(e)}function ux(t,e){if(t==="input"||t==="change")return Ha(e)}function dx(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var vt=typeof Object.is=="function"?Object.is:dx;function Ei(t,e){if(vt(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!qo.call(e,i)||!vt(t[i],e[i]))return!1}return!0}function Iu(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Uu(t,e){var n=Iu(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Iu(n)}}function Jf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Jf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function em(){for(var t=window,e=Zs();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Zs(t.document)}return e}function Nc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function hx(t){var e=em(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&Jf(n.ownerDocument.documentElement,n)){if(r!==null&&Nc(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!t.extend&&s>r&&(i=r,r=s,s=i),i=Uu(n,s);var a=Uu(n,r);i&&a&&(t.rangeCount!==1||t.anchorNode!==i.node||t.anchorOffset!==i.offset||t.focusNode!==a.node||t.focusOffset!==a.offset)&&(e=e.createRange(),e.setStart(i.node,i.offset),t.removeAllRanges(),s>r?(t.addRange(e),t.extend(a.node,a.offset)):(e.setEnd(a.node,a.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var fx=Ut&&"documentMode"in document&&11>=document.documentMode,ar=null,gl=null,gi=null,xl=!1;function Hu(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;xl||ar==null||ar!==Zs(r)||(r=ar,"selectionStart"in r&&Nc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),gi&&Ei(gi,r)||(gi=r,r=sa(gl,"onSelect"),0<r.length&&(e=new wc("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=ar)))}function hs(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var or={animationend:hs("Animation","AnimationEnd"),animationiteration:hs("Animation","AnimationIteration"),animationstart:hs("Animation","AnimationStart"),transitionend:hs("Transition","TransitionEnd")},wo={},tm={};Ut&&(tm=document.createElement("div").style,"AnimationEvent"in window||(delete or.animationend.animation,delete or.animationiteration.animation,delete or.animationstart.animation),"TransitionEvent"in window||delete or.transitionend.transition);function Ba(t){if(wo[t])return wo[t];if(!or[t])return t;var e=or[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in tm)return wo[t]=e[n];return t}var nm=Ba("animationend"),rm=Ba("animationiteration"),im=Ba("animationstart"),sm=Ba("transitionend"),am=new Map,Bu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function wn(t,e){am.set(t,e),Gn(e,[t])}for(var So=0;So<Bu.length;So++){var _o=Bu[So],mx=_o.toLowerCase(),px=_o[0].toUpperCase()+_o.slice(1);wn(mx,"on"+px)}wn(nm,"onAnimationEnd");wn(rm,"onAnimationIteration");wn(im,"onAnimationStart");wn("dblclick","onDoubleClick");wn("focusin","onFocus");wn("focusout","onBlur");wn(sm,"onTransitionEnd");kr("onMouseEnter",["mouseout","mouseover"]);kr("onMouseLeave",["mouseout","mouseover"]);kr("onPointerEnter",["pointerout","pointerover"]);kr("onPointerLeave",["pointerout","pointerover"]);Gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Gn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ai="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),gx=new Set("cancel close invalid load scroll toggle".split(" ").concat(ai));function Wu(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,m0(r,e,void 0,t),t.currentTarget=null}function om(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],i=r.event;r=r.listeners;e:{var s=void 0;if(e)for(var a=r.length-1;0<=a;a--){var o=r[a],l=o.instance,u=o.currentTarget;if(o=o.listener,l!==s&&i.isPropagationStopped())break e;Wu(i,o,u),s=l}else for(a=0;a<r.length;a++){if(o=r[a],l=o.instance,u=o.currentTarget,o=o.listener,l!==s&&i.isPropagationStopped())break e;Wu(i,o,u),s=l}}}if(ea)throw t=hl,ea=!1,hl=null,t}function ee(t,e){var n=e[Sl];n===void 0&&(n=e[Sl]=new Set);var r=t+"__bubble";n.has(r)||(lm(e,t,2,!1),n.add(r))}function No(t,e,n){var r=0;e&&(r|=4),lm(n,t,r,e)}var fs="_reactListening"+Math.random().toString(36).slice(2);function Ti(t){if(!t[fs]){t[fs]=!0,pf.forEach(function(n){n!=="selectionchange"&&(gx.has(n)||No(n,!1,t),No(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[fs]||(e[fs]=!0,No("selectionchange",!1,e))}}function lm(t,e,n,r){switch(Vf(e)){case 1:var i=$0;break;case 4:i=E0;break;default:i=vc}n=i.bind(null,e,n,t),i=void 0,!dl||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),r?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function ko(t,e,n,r,i){var s=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var a=r.tag;if(a===3||a===4){var o=r.stateNode.containerInfo;if(o===i||o.nodeType===8&&o.parentNode===i)break;if(a===4)for(a=r.return;a!==null;){var l=a.tag;if((l===3||l===4)&&(l=a.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;a=a.return}for(;o!==null;){if(a=On(o),a===null)return;if(l=a.tag,l===5||l===6){r=s=a;continue e}o=o.parentNode}}r=r.return}Ef(function(){var u=s,d=pc(n),h=[];e:{var f=am.get(t);if(f!==void 0){var m=wc,x=t;switch(t){case"keypress":if(As(n)===0)break e;case"keydown":case"keyup":m=Y0;break;case"focusin":x="focus",m=yo;break;case"focusout":x="blur",m=yo;break;case"beforeblur":case"afterblur":m=yo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=Tu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=D0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=K0;break;case nm:case rm:case im:m=z0;break;case sm:m=q0;break;case"scroll":m=T0;break;case"wheel":m=J0;break;case"copy":case"cut":case"paste":m=A0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Du}var y=(e&4)!==0,b=!y&&t==="scroll",g=y?f!==null?f+"Capture":null:f;y=[];for(var p=u,v;p!==null;){v=p;var w=v.stateNode;if(v.tag===5&&w!==null&&(v=w,g!==null&&(w=ji(p,g),w!=null&&y.push(Ri(p,w,v)))),b)break;p=p.return}0<y.length&&(f=new m(f,x,null,n,d),h.push({event:f,listeners:y}))}}if(!(e&7)){e:{if(f=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",f&&n!==cl&&(x=n.relatedTarget||n.fromElement)&&(On(x)||x[Ht]))break e;if((m||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=u,x=x?On(x):null,x!==null&&(b=qn(x),x!==b||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=u),m!==x)){if(y=Tu,w="onMouseLeave",g="onMouseEnter",p="mouse",(t==="pointerout"||t==="pointerover")&&(y=Du,w="onPointerLeave",g="onPointerEnter",p="pointer"),b=m==null?f:lr(m),v=x==null?f:lr(x),f=new y(w,p+"leave",m,n,d),f.target=b,f.relatedTarget=v,w=null,On(d)===u&&(y=new y(g,p+"enter",x,n,d),y.target=v,y.relatedTarget=b,w=y),b=w,m&&x)t:{for(y=m,g=x,p=0,v=y;v;v=Jn(v))p++;for(v=0,w=g;w;w=Jn(w))v++;for(;0<p-v;)y=Jn(y),p--;for(;0<v-p;)g=Jn(g),v--;for(;p--;){if(y===g||g!==null&&y===g.alternate)break t;y=Jn(y),g=Jn(g)}y=null}else y=null;m!==null&&Vu(h,f,m,y,!1),x!==null&&b!==null&&Vu(h,b,x,y,!0)}}e:{if(f=u?lr(u):window,m=f.nodeName&&f.nodeName.toLowerCase(),m==="select"||m==="input"&&f.type==="file")var _=ax;else if(zu(f))if(qf)_=ux;else{_=lx;var S=ox}else(m=f.nodeName)&&m.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(_=cx);if(_&&(_=_(t,u))){Gf(h,_,n,d);break e}S&&S(t,f,u),t==="focusout"&&(S=f._wrapperState)&&S.controlled&&f.type==="number"&&il(f,"number",f.value)}switch(S=u?lr(u):window,t){case"focusin":(zu(S)||S.contentEditable==="true")&&(ar=S,gl=u,gi=null);break;case"focusout":gi=gl=ar=null;break;case"mousedown":xl=!0;break;case"contextmenu":case"mouseup":case"dragend":xl=!1,Hu(h,n,d);break;case"selectionchange":if(fx)break;case"keydown":case"keyup":Hu(h,n,d)}var N;if(_c)e:{switch(t){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else sr?Qf(t,n)&&(j="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Xf&&n.locale!=="ko"&&(sr||j!=="onCompositionStart"?j==="onCompositionEnd"&&sr&&(N=Yf()):(en=d,bc="value"in en?en.value:en.textContent,sr=!0)),S=sa(u,j),0<S.length&&(j=new Ru(j,t,null,n,d),h.push({event:j,listeners:S}),N?j.data=N:(N=Kf(n),N!==null&&(j.data=N)))),(N=tx?nx(t,n):rx(t,n))&&(u=sa(u,"onBeforeInput"),0<u.length&&(d=new Ru("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:u}),d.data=N))}om(h,e)})}function Ri(t,e,n){return{instance:t,listener:e,currentTarget:n}}function sa(t,e){for(var n=e+"Capture",r=[];t!==null;){var i=t,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=ji(t,n),s!=null&&r.unshift(Ri(t,s,i)),s=ji(t,e),s!=null&&r.push(Ri(t,s,i))),t=t.return}return r}function Jn(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function Vu(t,e,n,r,i){for(var s=e._reactName,a=[];n!==null&&n!==r;){var o=n,l=o.alternate,u=o.stateNode;if(l!==null&&l===r)break;o.tag===5&&u!==null&&(o=u,i?(l=ji(n,s),l!=null&&a.unshift(Ri(n,l,o))):i||(l=ji(n,s),l!=null&&a.push(Ri(n,l,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var xx=/\r\n?/g,yx=/\u0000|\uFFFD/g;function Yu(t){return(typeof t=="string"?t:""+t).replace(xx,`
`).replace(yx,"")}function ms(t,e,n){if(e=Yu(e),Yu(t)!==e&&n)throw Error(C(425))}function aa(){}var yl=null,vl=null;function bl(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var wl=typeof setTimeout=="function"?setTimeout:void 0,vx=typeof clearTimeout=="function"?clearTimeout:void 0,Xu=typeof Promise=="function"?Promise:void 0,bx=typeof queueMicrotask=="function"?queueMicrotask:typeof Xu<"u"?function(t){return Xu.resolve(null).then(t).catch(wx)}:wl;function wx(t){setTimeout(function(){throw t})}function jo(t,e){var n=e,r=0;do{var i=n.nextSibling;if(t.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){t.removeChild(i),Mi(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Mi(e)}function cn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function Qu(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var zr=Math.random().toString(36).slice(2),jt="__reactFiber$"+zr,Di="__reactProps$"+zr,Ht="__reactContainer$"+zr,Sl="__reactEvents$"+zr,Sx="__reactListeners$"+zr,_x="__reactHandles$"+zr;function On(t){var e=t[jt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ht]||n[jt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Qu(t);t!==null;){if(n=t[jt])return n;t=Qu(t)}return e}t=n,n=t.parentNode}return null}function Zi(t){return t=t[jt]||t[Ht],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function lr(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(C(33))}function Wa(t){return t[Di]||null}var _l=[],cr=-1;function Sn(t){return{current:t}}function ne(t){0>cr||(t.current=_l[cr],_l[cr]=null,cr--)}function Z(t,e){cr++,_l[cr]=t.current,t.current=e}var vn={},Me=Sn(vn),Be=Sn(!1),Hn=vn;function jr(t,e){var n=t.type.contextTypes;if(!n)return vn;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=e[s];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=i),i}function We(t){return t=t.childContextTypes,t!=null}function oa(){ne(Be),ne(Me)}function Ku(t,e,n){if(Me.current!==vn)throw Error(C(168));Z(Me,e),Z(Be,n)}function cm(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in e))throw Error(C(108,o0(t)||"Unknown",i));return ae({},n,r)}function la(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||vn,Hn=Me.current,Z(Me,t),Z(Be,Be.current),!0}function Gu(t,e,n){var r=t.stateNode;if(!r)throw Error(C(169));n?(t=cm(t,e,Hn),r.__reactInternalMemoizedMergedChildContext=t,ne(Be),ne(Me),Z(Me,t)):ne(Be),Z(Be,n)}var Ot=null,Va=!1,Co=!1;function um(t){Ot===null?Ot=[t]:Ot.push(t)}function Nx(t){Va=!0,um(t)}function _n(){if(!Co&&Ot!==null){Co=!0;var t=0,e=Q;try{var n=Ot;for(Q=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}Ot=null,Va=!1}catch(i){throw Ot!==null&&(Ot=Ot.slice(t+1)),Of(gc,_n),i}finally{Q=e,Co=!1}}return null}var ur=[],dr=0,ca=null,ua=0,tt=[],nt=0,Bn=null,zt=1,Ft="";function $n(t,e){ur[dr++]=ua,ur[dr++]=ca,ca=t,ua=e}function dm(t,e,n){tt[nt++]=zt,tt[nt++]=Ft,tt[nt++]=Bn,Bn=t;var r=zt;t=Ft;var i=32-xt(r)-1;r&=~(1<<i),n+=1;var s=32-xt(e)+i;if(30<s){var a=i-i%5;s=(r&(1<<a)-1).toString(32),r>>=a,i-=a,zt=1<<32-xt(e)+i|n<<i|r,Ft=s+t}else zt=1<<s|n<<i|r,Ft=t}function kc(t){t.return!==null&&($n(t,1),dm(t,1,0))}function jc(t){for(;t===ca;)ca=ur[--dr],ur[dr]=null,ua=ur[--dr],ur[dr]=null;for(;t===Bn;)Bn=tt[--nt],tt[nt]=null,Ft=tt[--nt],tt[nt]=null,zt=tt[--nt],tt[nt]=null}var Ge=null,Ke=null,re=!1,ft=null;function hm(t,e){var n=rt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function qu(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Ge=t,Ke=cn(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Ge=t,Ke=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Bn!==null?{id:zt,overflow:Ft}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=rt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Ge=t,Ke=null,!0):!1;default:return!1}}function Nl(t){return(t.mode&1)!==0&&(t.flags&128)===0}function kl(t){if(re){var e=Ke;if(e){var n=e;if(!qu(t,e)){if(Nl(t))throw Error(C(418));e=cn(n.nextSibling);var r=Ge;e&&qu(t,e)?hm(r,n):(t.flags=t.flags&-4097|2,re=!1,Ge=t)}}else{if(Nl(t))throw Error(C(418));t.flags=t.flags&-4097|2,re=!1,Ge=t}}}function Zu(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Ge=t}function ps(t){if(t!==Ge)return!1;if(!re)return Zu(t),re=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!bl(t.type,t.memoizedProps)),e&&(e=Ke)){if(Nl(t))throw fm(),Error(C(418));for(;e;)hm(t,e),e=cn(e.nextSibling)}if(Zu(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(C(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Ke=cn(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Ke=null}}else Ke=Ge?cn(t.stateNode.nextSibling):null;return!0}function fm(){for(var t=Ke;t;)t=cn(t.nextSibling)}function Cr(){Ke=Ge=null,re=!1}function Cc(t){ft===null?ft=[t]:ft.push(t)}var kx=Yt.ReactCurrentBatchConfig;function Xr(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,t));var i=r,s=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===s?e.ref:(e=function(a){var o=i.refs;a===null?delete o[s]:o[s]=a},e._stringRef=s,e)}if(typeof t!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,t))}return t}function gs(t,e){throw t=Object.prototype.toString.call(e),Error(C(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function Ju(t){var e=t._init;return e(t._payload)}function mm(t){function e(g,p){if(t){var v=g.deletions;v===null?(g.deletions=[p],g.flags|=16):v.push(p)}}function n(g,p){if(!t)return null;for(;p!==null;)e(g,p),p=p.sibling;return null}function r(g,p){for(g=new Map;p!==null;)p.key!==null?g.set(p.key,p):g.set(p.index,p),p=p.sibling;return g}function i(g,p){return g=fn(g,p),g.index=0,g.sibling=null,g}function s(g,p,v){return g.index=v,t?(v=g.alternate,v!==null?(v=v.index,v<p?(g.flags|=2,p):v):(g.flags|=2,p)):(g.flags|=1048576,p)}function a(g){return t&&g.alternate===null&&(g.flags|=2),g}function o(g,p,v,w){return p===null||p.tag!==6?(p=Do(v,g.mode,w),p.return=g,p):(p=i(p,v),p.return=g,p)}function l(g,p,v,w){var _=v.type;return _===ir?d(g,p,v.props.children,w,v.key):p!==null&&(p.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===Gt&&Ju(_)===p.type)?(w=i(p,v.props),w.ref=Xr(g,p,v),w.return=g,w):(w=Ys(v.type,v.key,v.props,null,g.mode,w),w.ref=Xr(g,p,v),w.return=g,w)}function u(g,p,v,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==v.containerInfo||p.stateNode.implementation!==v.implementation?(p=Oo(v,g.mode,w),p.return=g,p):(p=i(p,v.children||[]),p.return=g,p)}function d(g,p,v,w,_){return p===null||p.tag!==7?(p=In(v,g.mode,w,_),p.return=g,p):(p=i(p,v),p.return=g,p)}function h(g,p,v){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Do(""+p,g.mode,v),p.return=g,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ss:return v=Ys(p.type,p.key,p.props,null,g.mode,v),v.ref=Xr(g,null,p),v.return=g,v;case rr:return p=Oo(p,g.mode,v),p.return=g,p;case Gt:var w=p._init;return h(g,w(p._payload),v)}if(ii(p)||Hr(p))return p=In(p,g.mode,v,null),p.return=g,p;gs(g,p)}return null}function f(g,p,v,w){var _=p!==null?p.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return _!==null?null:o(g,p,""+v,w);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case ss:return v.key===_?l(g,p,v,w):null;case rr:return v.key===_?u(g,p,v,w):null;case Gt:return _=v._init,f(g,p,_(v._payload),w)}if(ii(v)||Hr(v))return _!==null?null:d(g,p,v,w,null);gs(g,v)}return null}function m(g,p,v,w,_){if(typeof w=="string"&&w!==""||typeof w=="number")return g=g.get(v)||null,o(p,g,""+w,_);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ss:return g=g.get(w.key===null?v:w.key)||null,l(p,g,w,_);case rr:return g=g.get(w.key===null?v:w.key)||null,u(p,g,w,_);case Gt:var S=w._init;return m(g,p,v,S(w._payload),_)}if(ii(w)||Hr(w))return g=g.get(v)||null,d(p,g,w,_,null);gs(p,w)}return null}function x(g,p,v,w){for(var _=null,S=null,N=p,j=p=0,$=null;N!==null&&j<v.length;j++){N.index>j?($=N,N=null):$=N.sibling;var M=f(g,N,v[j],w);if(M===null){N===null&&(N=$);break}t&&N&&M.alternate===null&&e(g,N),p=s(M,p,j),S===null?_=M:S.sibling=M,S=M,N=$}if(j===v.length)return n(g,N),re&&$n(g,j),_;if(N===null){for(;j<v.length;j++)N=h(g,v[j],w),N!==null&&(p=s(N,p,j),S===null?_=N:S.sibling=N,S=N);return re&&$n(g,j),_}for(N=r(g,N);j<v.length;j++)$=m(N,g,j,v[j],w),$!==null&&(t&&$.alternate!==null&&N.delete($.key===null?j:$.key),p=s($,p,j),S===null?_=$:S.sibling=$,S=$);return t&&N.forEach(function(E){return e(g,E)}),re&&$n(g,j),_}function y(g,p,v,w){var _=Hr(v);if(typeof _!="function")throw Error(C(150));if(v=_.call(v),v==null)throw Error(C(151));for(var S=_=null,N=p,j=p=0,$=null,M=v.next();N!==null&&!M.done;j++,M=v.next()){N.index>j?($=N,N=null):$=N.sibling;var E=f(g,N,M.value,w);if(E===null){N===null&&(N=$);break}t&&N&&E.alternate===null&&e(g,N),p=s(E,p,j),S===null?_=E:S.sibling=E,S=E,N=$}if(M.done)return n(g,N),re&&$n(g,j),_;if(N===null){for(;!M.done;j++,M=v.next())M=h(g,M.value,w),M!==null&&(p=s(M,p,j),S===null?_=M:S.sibling=M,S=M);return re&&$n(g,j),_}for(N=r(g,N);!M.done;j++,M=v.next())M=m(N,g,j,M.value,w),M!==null&&(t&&M.alternate!==null&&N.delete(M.key===null?j:M.key),p=s(M,p,j),S===null?_=M:S.sibling=M,S=M);return t&&N.forEach(function(L){return e(g,L)}),re&&$n(g,j),_}function b(g,p,v,w){if(typeof v=="object"&&v!==null&&v.type===ir&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case ss:e:{for(var _=v.key,S=p;S!==null;){if(S.key===_){if(_=v.type,_===ir){if(S.tag===7){n(g,S.sibling),p=i(S,v.props.children),p.return=g,g=p;break e}}else if(S.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===Gt&&Ju(_)===S.type){n(g,S.sibling),p=i(S,v.props),p.ref=Xr(g,S,v),p.return=g,g=p;break e}n(g,S);break}else e(g,S);S=S.sibling}v.type===ir?(p=In(v.props.children,g.mode,w,v.key),p.return=g,g=p):(w=Ys(v.type,v.key,v.props,null,g.mode,w),w.ref=Xr(g,p,v),w.return=g,g=w)}return a(g);case rr:e:{for(S=v.key;p!==null;){if(p.key===S)if(p.tag===4&&p.stateNode.containerInfo===v.containerInfo&&p.stateNode.implementation===v.implementation){n(g,p.sibling),p=i(p,v.children||[]),p.return=g,g=p;break e}else{n(g,p);break}else e(g,p);p=p.sibling}p=Oo(v,g.mode,w),p.return=g,g=p}return a(g);case Gt:return S=v._init,b(g,p,S(v._payload),w)}if(ii(v))return x(g,p,v,w);if(Hr(v))return y(g,p,v,w);gs(g,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,p!==null&&p.tag===6?(n(g,p.sibling),p=i(p,v),p.return=g,g=p):(n(g,p),p=Do(v,g.mode,w),p.return=g,g=p),a(g)):n(g,p)}return b}var Pr=mm(!0),pm=mm(!1),da=Sn(null),ha=null,hr=null,Pc=null;function Mc(){Pc=hr=ha=null}function $c(t){var e=da.current;ne(da),t._currentValue=e}function jl(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function vr(t,e){ha=t,Pc=hr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(Ue=!0),t.firstContext=null)}function at(t){var e=t._currentValue;if(Pc!==t)if(t={context:t,memoizedValue:e,next:null},hr===null){if(ha===null)throw Error(C(308));hr=t,ha.dependencies={lanes:0,firstContext:t}}else hr=hr.next=t;return e}var Ln=null;function Ec(t){Ln===null?Ln=[t]:Ln.push(t)}function gm(t,e,n,r){var i=e.interleaved;return i===null?(n.next=n,Ec(e)):(n.next=i.next,i.next=n),e.interleaved=n,Bt(t,r)}function Bt(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var qt=!1;function Tc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function xm(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function It(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function un(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,H&2){var i=r.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),r.pending=e,Bt(t,n)}return i=r.interleaved,i===null?(e.next=e,Ec(r)):(e.next=i.next,i.next=e),r.interleaved=e,Bt(t,n)}function Is(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,xc(t,n)}}function ed(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=a:s=s.next=a,n=n.next}while(n!==null);s===null?i=s=e:s=s.next=e}else i=s=e;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function fa(t,e,n,r){var i=t.updateQueue;qt=!1;var s=i.firstBaseUpdate,a=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var l=o,u=l.next;l.next=null,a===null?s=u:a.next=u,a=l;var d=t.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==a&&(o===null?d.firstBaseUpdate=u:o.next=u,d.lastBaseUpdate=l))}if(s!==null){var h=i.baseState;a=0,d=u=l=null,o=s;do{var f=o.lane,m=o.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:m,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var x=t,y=o;switch(f=e,m=n,y.tag){case 1:if(x=y.payload,typeof x=="function"){h=x.call(m,h,f);break e}h=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=y.payload,f=typeof x=="function"?x.call(m,h,f):x,f==null)break e;h=ae({},h,f);break e;case 2:qt=!0}}o.callback!==null&&o.lane!==0&&(t.flags|=64,f=i.effects,f===null?i.effects=[o]:f.push(o))}else m={eventTime:m,lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(u=d=m,l=h):d=d.next=m,a|=f;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;f=o,o=f.next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}while(!0);if(d===null&&(l=h),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=d,e=i.shared.interleaved,e!==null){i=e;do a|=i.lane,i=i.next;while(i!==e)}else s===null&&(i.shared.lanes=0);Vn|=a,t.lanes=a,t.memoizedState=h}}function td(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var Ji={},Pt=Sn(Ji),Oi=Sn(Ji),Li=Sn(Ji);function zn(t){if(t===Ji)throw Error(C(174));return t}function Rc(t,e){switch(Z(Li,e),Z(Oi,t),Z(Pt,Ji),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:al(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=al(e,t)}ne(Pt),Z(Pt,e)}function Mr(){ne(Pt),ne(Oi),ne(Li)}function ym(t){zn(Li.current);var e=zn(Pt.current),n=al(e,t.type);e!==n&&(Z(Oi,t),Z(Pt,n))}function Dc(t){Oi.current===t&&(ne(Pt),ne(Oi))}var ie=Sn(0);function ma(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Po=[];function Oc(){for(var t=0;t<Po.length;t++)Po[t]._workInProgressVersionPrimary=null;Po.length=0}var Us=Yt.ReactCurrentDispatcher,Mo=Yt.ReactCurrentBatchConfig,Wn=0,se=null,pe=null,ve=null,pa=!1,xi=!1,zi=0,jx=0;function Ne(){throw Error(C(321))}function Lc(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!vt(t[n],e[n]))return!1;return!0}function zc(t,e,n,r,i,s){if(Wn=s,se=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,Us.current=t===null||t.memoizedState===null?$x:Ex,t=n(r,i),xi){s=0;do{if(xi=!1,zi=0,25<=s)throw Error(C(301));s+=1,ve=pe=null,e.updateQueue=null,Us.current=Tx,t=n(r,i)}while(xi)}if(Us.current=ga,e=pe!==null&&pe.next!==null,Wn=0,ve=pe=se=null,pa=!1,e)throw Error(C(300));return t}function Fc(){var t=zi!==0;return zi=0,t}function Nt(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ve===null?se.memoizedState=ve=t:ve=ve.next=t,ve}function ot(){if(pe===null){var t=se.alternate;t=t!==null?t.memoizedState:null}else t=pe.next;var e=ve===null?se.memoizedState:ve.next;if(e!==null)ve=e,pe=t;else{if(t===null)throw Error(C(310));pe=t,t={memoizedState:pe.memoizedState,baseState:pe.baseState,baseQueue:pe.baseQueue,queue:pe.queue,next:null},ve===null?se.memoizedState=ve=t:ve=ve.next=t}return ve}function Fi(t,e){return typeof e=="function"?e(t):e}function $o(t){var e=ot(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=pe,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var a=i.next;i.next=s.next,s.next=a}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var o=a=null,l=null,u=s;do{var d=u.lane;if((Wn&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var h={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(o=l=h,a=r):l=l.next=h,se.lanes|=d,Vn|=d}u=u.next}while(u!==null&&u!==s);l===null?a=r:l.next=o,vt(r,e.memoizedState)||(Ue=!0),e.memoizedState=r,e.baseState=a,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){i=t;do s=i.lane,se.lanes|=s,Vn|=s,i=i.next;while(i!==t)}else i===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function Eo(t){var e=ot(),n=e.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,s=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do s=t(s,a.action),a=a.next;while(a!==i);vt(s,e.memoizedState)||(Ue=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),n.lastRenderedState=s}return[s,r]}function vm(){}function bm(t,e){var n=se,r=ot(),i=e(),s=!vt(r.memoizedState,i);if(s&&(r.memoizedState=i,Ue=!0),r=r.queue,Ac(_m.bind(null,n,r,t),[t]),r.getSnapshot!==e||s||ve!==null&&ve.memoizedState.tag&1){if(n.flags|=2048,Ai(9,Sm.bind(null,n,r,i,e),void 0,null),be===null)throw Error(C(349));Wn&30||wm(n,e,i)}return i}function wm(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Sm(t,e,n,r){e.value=n,e.getSnapshot=r,Nm(e)&&km(t)}function _m(t,e,n){return n(function(){Nm(e)&&km(t)})}function Nm(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!vt(t,n)}catch{return!0}}function km(t){var e=Bt(t,1);e!==null&&yt(e,t,1,-1)}function nd(t){var e=Nt();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Fi,lastRenderedState:t},e.queue=t,t=t.dispatch=Mx.bind(null,se,t),[e.memoizedState,t]}function Ai(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=se.updateQueue,e===null?(e={lastEffect:null,stores:null},se.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function jm(){return ot().memoizedState}function Hs(t,e,n,r){var i=Nt();se.flags|=t,i.memoizedState=Ai(1|e,n,void 0,r===void 0?null:r)}function Ya(t,e,n,r){var i=ot();r=r===void 0?null:r;var s=void 0;if(pe!==null){var a=pe.memoizedState;if(s=a.destroy,r!==null&&Lc(r,a.deps)){i.memoizedState=Ai(e,n,s,r);return}}se.flags|=t,i.memoizedState=Ai(1|e,n,s,r)}function rd(t,e){return Hs(8390656,8,t,e)}function Ac(t,e){return Ya(2048,8,t,e)}function Cm(t,e){return Ya(4,2,t,e)}function Pm(t,e){return Ya(4,4,t,e)}function Mm(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function $m(t,e,n){return n=n!=null?n.concat([t]):null,Ya(4,4,Mm.bind(null,e,t),n)}function Ic(){}function Em(t,e){var n=ot();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function Tm(t,e){var n=ot();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Lc(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function Rm(t,e,n){return Wn&21?(vt(n,e)||(n=Ff(),se.lanes|=n,Vn|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,Ue=!0),t.memoizedState=n)}function Cx(t,e){var n=Q;Q=n!==0&&4>n?n:4,t(!0);var r=Mo.transition;Mo.transition={};try{t(!1),e()}finally{Q=n,Mo.transition=r}}function Dm(){return ot().memoizedState}function Px(t,e,n){var r=hn(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Om(t))Lm(e,n);else if(n=gm(t,e,n,r),n!==null){var i=De();yt(n,t,r,i),zm(n,e,r)}}function Mx(t,e,n){var r=hn(t),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Om(t))Lm(e,i);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var a=e.lastRenderedState,o=s(a,n);if(i.hasEagerState=!0,i.eagerState=o,vt(o,a)){var l=e.interleaved;l===null?(i.next=i,Ec(e)):(i.next=l.next,l.next=i),e.interleaved=i;return}}catch{}finally{}n=gm(t,e,i,r),n!==null&&(i=De(),yt(n,t,r,i),zm(n,e,r))}}function Om(t){var e=t.alternate;return t===se||e!==null&&e===se}function Lm(t,e){xi=pa=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function zm(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,xc(t,n)}}var ga={readContext:at,useCallback:Ne,useContext:Ne,useEffect:Ne,useImperativeHandle:Ne,useInsertionEffect:Ne,useLayoutEffect:Ne,useMemo:Ne,useReducer:Ne,useRef:Ne,useState:Ne,useDebugValue:Ne,useDeferredValue:Ne,useTransition:Ne,useMutableSource:Ne,useSyncExternalStore:Ne,useId:Ne,unstable_isNewReconciler:!1},$x={readContext:at,useCallback:function(t,e){return Nt().memoizedState=[t,e===void 0?null:e],t},useContext:at,useEffect:rd,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,Hs(4194308,4,Mm.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Hs(4194308,4,t,e)},useInsertionEffect:function(t,e){return Hs(4,2,t,e)},useMemo:function(t,e){var n=Nt();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Nt();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=Px.bind(null,se,t),[r.memoizedState,t]},useRef:function(t){var e=Nt();return t={current:t},e.memoizedState=t},useState:nd,useDebugValue:Ic,useDeferredValue:function(t){return Nt().memoizedState=t},useTransition:function(){var t=nd(!1),e=t[0];return t=Cx.bind(null,t[1]),Nt().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=se,i=Nt();if(re){if(n===void 0)throw Error(C(407));n=n()}else{if(n=e(),be===null)throw Error(C(349));Wn&30||wm(r,e,n)}i.memoizedState=n;var s={value:n,getSnapshot:e};return i.queue=s,rd(_m.bind(null,r,s,t),[t]),r.flags|=2048,Ai(9,Sm.bind(null,r,s,n,e),void 0,null),n},useId:function(){var t=Nt(),e=be.identifierPrefix;if(re){var n=Ft,r=zt;n=(r&~(1<<32-xt(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=zi++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=jx++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},Ex={readContext:at,useCallback:Em,useContext:at,useEffect:Ac,useImperativeHandle:$m,useInsertionEffect:Cm,useLayoutEffect:Pm,useMemo:Tm,useReducer:$o,useRef:jm,useState:function(){return $o(Fi)},useDebugValue:Ic,useDeferredValue:function(t){var e=ot();return Rm(e,pe.memoizedState,t)},useTransition:function(){var t=$o(Fi)[0],e=ot().memoizedState;return[t,e]},useMutableSource:vm,useSyncExternalStore:bm,useId:Dm,unstable_isNewReconciler:!1},Tx={readContext:at,useCallback:Em,useContext:at,useEffect:Ac,useImperativeHandle:$m,useInsertionEffect:Cm,useLayoutEffect:Pm,useMemo:Tm,useReducer:Eo,useRef:jm,useState:function(){return Eo(Fi)},useDebugValue:Ic,useDeferredValue:function(t){var e=ot();return pe===null?e.memoizedState=t:Rm(e,pe.memoizedState,t)},useTransition:function(){var t=Eo(Fi)[0],e=ot().memoizedState;return[t,e]},useMutableSource:vm,useSyncExternalStore:bm,useId:Dm,unstable_isNewReconciler:!1};function dt(t,e){if(t&&t.defaultProps){e=ae({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}function Cl(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:ae({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Xa={isMounted:function(t){return(t=t._reactInternals)?qn(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=De(),i=hn(t),s=It(r,i);s.payload=e,n!=null&&(s.callback=n),e=un(t,s,i),e!==null&&(yt(e,t,i,r),Is(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=De(),i=hn(t),s=It(r,i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=un(t,s,i),e!==null&&(yt(e,t,i,r),Is(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=De(),r=hn(t),i=It(n,r);i.tag=2,e!=null&&(i.callback=e),e=un(t,i,r),e!==null&&(yt(e,t,r,n),Is(e,t,r))}};function id(t,e,n,r,i,s,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,s,a):e.prototype&&e.prototype.isPureReactComponent?!Ei(n,r)||!Ei(i,s):!0}function Fm(t,e,n){var r=!1,i=vn,s=e.contextType;return typeof s=="object"&&s!==null?s=at(s):(i=We(e)?Hn:Me.current,r=e.contextTypes,s=(r=r!=null)?jr(t,i):vn),e=new e(n,s),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Xa,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=s),e}function sd(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Xa.enqueueReplaceState(e,e.state,null)}function Pl(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs={},Tc(t);var s=e.contextType;typeof s=="object"&&s!==null?i.context=at(s):(s=We(e)?Hn:Me.current,i.context=jr(t,s)),i.state=t.memoizedState,s=e.getDerivedStateFromProps,typeof s=="function"&&(Cl(t,e,s,n),i.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(e=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),e!==i.state&&Xa.enqueueReplaceState(i,i.state,null),fa(t,n,i,r),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308)}function $r(t,e){try{var n="",r=e;do n+=a0(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:t,source:e,stack:i,digest:null}}function To(t,e,n){return{value:t,source:null,stack:n??null,digest:e??null}}function Ml(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Rx=typeof WeakMap=="function"?WeakMap:Map;function Am(t,e,n){n=It(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){ya||(ya=!0,Al=r),Ml(t,e)},n}function Im(t,e,n){n=It(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var i=e.value;n.payload=function(){return r(i)},n.callback=function(){Ml(t,e)}}var s=t.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ml(t,e),typeof r!="function"&&(dn===null?dn=new Set([this]):dn.add(this));var a=e.stack;this.componentDidCatch(e.value,{componentStack:a!==null?a:""})}),n}function ad(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Rx;var i=new Set;r.set(e,i)}else i=r.get(e),i===void 0&&(i=new Set,r.set(e,i));i.has(n)||(i.add(n),t=Xx.bind(null,t,e,n),e.then(t,t))}function od(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function ld(t,e,n,r,i){return t.mode&1?(t.flags|=65536,t.lanes=i,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=It(-1,1),e.tag=2,un(n,e,1))),n.lanes|=1),t)}var Dx=Yt.ReactCurrentOwner,Ue=!1;function Te(t,e,n,r){e.child=t===null?pm(e,null,n,r):Pr(e,t.child,n,r)}function cd(t,e,n,r,i){n=n.render;var s=e.ref;return vr(e,i),r=zc(t,e,n,r,s,i),n=Fc(),t!==null&&!Ue?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Wt(t,e,i)):(re&&n&&kc(e),e.flags|=1,Te(t,e,r,i),e.child)}function ud(t,e,n,r,i){if(t===null){var s=n.type;return typeof s=="function"&&!Qc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=s,Um(t,e,s,r,i)):(t=Ys(n.type,null,r,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!(t.lanes&i)){var a=s.memoizedProps;if(n=n.compare,n=n!==null?n:Ei,n(a,r)&&t.ref===e.ref)return Wt(t,e,i)}return e.flags|=1,t=fn(s,r),t.ref=e.ref,t.return=e,e.child=t}function Um(t,e,n,r,i){if(t!==null){var s=t.memoizedProps;if(Ei(s,r)&&t.ref===e.ref)if(Ue=!1,e.pendingProps=r=s,(t.lanes&i)!==0)t.flags&131072&&(Ue=!0);else return e.lanes=t.lanes,Wt(t,e,i)}return $l(t,e,n,r,i)}function Hm(t,e,n){var r=e.pendingProps,i=r.children,s=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},Z(mr,Qe),Qe|=n;else{if(!(n&1073741824))return t=s!==null?s.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,Z(mr,Qe),Qe|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,Z(mr,Qe),Qe|=r}else s!==null?(r=s.baseLanes|n,e.memoizedState=null):r=n,Z(mr,Qe),Qe|=r;return Te(t,e,i,n),e.child}function Bm(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function $l(t,e,n,r,i){var s=We(n)?Hn:Me.current;return s=jr(e,s),vr(e,i),n=zc(t,e,n,r,s,i),r=Fc(),t!==null&&!Ue?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i,Wt(t,e,i)):(re&&r&&kc(e),e.flags|=1,Te(t,e,n,i),e.child)}function dd(t,e,n,r,i){if(We(n)){var s=!0;la(e)}else s=!1;if(vr(e,i),e.stateNode===null)Bs(t,e),Fm(e,n,r),Pl(e,n,r,i),r=!0;else if(t===null){var a=e.stateNode,o=e.memoizedProps;a.props=o;var l=a.context,u=n.contextType;typeof u=="object"&&u!==null?u=at(u):(u=We(n)?Hn:Me.current,u=jr(e,u));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof a.getSnapshotBeforeUpdate=="function";h||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==r||l!==u)&&sd(e,a,r,u),qt=!1;var f=e.memoizedState;a.state=f,fa(e,r,a,i),l=e.memoizedState,o!==r||f!==l||Be.current||qt?(typeof d=="function"&&(Cl(e,n,d,r),l=e.memoizedState),(o=qt||id(e,n,o,r,f,l,u))?(h||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(e.flags|=4194308)):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),a.props=r,a.state=l,a.context=u,r=o):(typeof a.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{a=e.stateNode,xm(t,e),o=e.memoizedProps,u=e.type===e.elementType?o:dt(e.type,o),a.props=u,h=e.pendingProps,f=a.context,l=n.contextType,typeof l=="object"&&l!==null?l=at(l):(l=We(n)?Hn:Me.current,l=jr(e,l));var m=n.getDerivedStateFromProps;(d=typeof m=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(o!==h||f!==l)&&sd(e,a,r,l),qt=!1,f=e.memoizedState,a.state=f,fa(e,r,a,i);var x=e.memoizedState;o!==h||f!==x||Be.current||qt?(typeof m=="function"&&(Cl(e,n,m,r),x=e.memoizedState),(u=qt||id(e,n,u,r,f,x,l)||!1)?(d||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(r,x,l),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(r,x,l)),typeof a.componentDidUpdate=="function"&&(e.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=x),a.props=r,a.state=x,a.context=l,r=u):(typeof a.componentDidUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&f===t.memoizedState||(e.flags|=1024),r=!1)}return El(t,e,n,r,s,i)}function El(t,e,n,r,i,s){Bm(t,e);var a=(e.flags&128)!==0;if(!r&&!a)return i&&Gu(e,n,!1),Wt(t,e,s);r=e.stateNode,Dx.current=e;var o=a&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&a?(e.child=Pr(e,t.child,null,s),e.child=Pr(e,null,o,s)):Te(t,e,o,s),e.memoizedState=r.state,i&&Gu(e,n,!0),e.child}function Wm(t){var e=t.stateNode;e.pendingContext?Ku(t,e.pendingContext,e.pendingContext!==e.context):e.context&&Ku(t,e.context,!1),Rc(t,e.containerInfo)}function hd(t,e,n,r,i){return Cr(),Cc(i),e.flags|=256,Te(t,e,n,r),e.child}var Tl={dehydrated:null,treeContext:null,retryLane:0};function Rl(t){return{baseLanes:t,cachePool:null,transitions:null}}function Vm(t,e,n){var r=e.pendingProps,i=ie.current,s=!1,a=(e.flags&128)!==0,o;if((o=a)||(o=t!==null&&t.memoizedState===null?!1:(i&2)!==0),o?(s=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(i|=1),Z(ie,i&1),t===null)return kl(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(a=r.children,t=r.fallback,s?(r=e.mode,s=e.child,a={mode:"hidden",children:a},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=a):s=Ga(a,r,0,null),t=In(t,r,n,null),s.return=e,t.return=e,s.sibling=t,e.child=s,e.child.memoizedState=Rl(n),e.memoizedState=Tl,t):Uc(e,a));if(i=t.memoizedState,i!==null&&(o=i.dehydrated,o!==null))return Ox(t,e,a,r,o,i,n);if(s){s=r.fallback,a=e.mode,i=t.child,o=i.sibling;var l={mode:"hidden",children:r.children};return!(a&1)&&e.child!==i?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=fn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),o!==null?s=fn(o,s):(s=In(s,a,n,null),s.flags|=2),s.return=e,r.return=e,r.sibling=s,e.child=r,r=s,s=e.child,a=t.child.memoizedState,a=a===null?Rl(n):{baseLanes:a.baseLanes|n,cachePool:null,transitions:a.transitions},s.memoizedState=a,s.childLanes=t.childLanes&~n,e.memoizedState=Tl,r}return s=t.child,t=s.sibling,r=fn(s,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function Uc(t,e){return e=Ga({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function xs(t,e,n,r){return r!==null&&Cc(r),Pr(e,t.child,null,n),t=Uc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Ox(t,e,n,r,i,s,a){if(n)return e.flags&256?(e.flags&=-257,r=To(Error(C(422))),xs(t,e,a,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(s=r.fallback,i=e.mode,r=Ga({mode:"visible",children:r.children},i,0,null),s=In(s,i,a,null),s.flags|=2,r.return=e,s.return=e,r.sibling=s,e.child=r,e.mode&1&&Pr(e,t.child,null,a),e.child.memoizedState=Rl(a),e.memoizedState=Tl,s);if(!(e.mode&1))return xs(t,e,a,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var o=r.dgst;return r=o,s=Error(C(419)),r=To(s,r,void 0),xs(t,e,a,r)}if(o=(a&t.childLanes)!==0,Ue||o){if(r=be,r!==null){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|a)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Bt(t,i),yt(r,t,i,-1))}return Xc(),r=To(Error(C(421))),xs(t,e,a,r)}return i.data==="$?"?(e.flags|=128,e.child=t.child,e=Qx.bind(null,t),i._reactRetry=e,null):(t=s.treeContext,Ke=cn(i.nextSibling),Ge=e,re=!0,ft=null,t!==null&&(tt[nt++]=zt,tt[nt++]=Ft,tt[nt++]=Bn,zt=t.id,Ft=t.overflow,Bn=e),e=Uc(e,r.children),e.flags|=4096,e)}function fd(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),jl(t.return,e,n)}function Ro(t,e,n,r,i){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Ym(t,e,n){var r=e.pendingProps,i=r.revealOrder,s=r.tail;if(Te(t,e,r.children,n),r=ie.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&fd(t,n,e);else if(t.tag===19)fd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(Z(ie,r),!(e.mode&1))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&ma(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Ro(e,!1,i,n,s);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&ma(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Ro(e,!0,n,null,s);break;case"together":Ro(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Bs(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function Wt(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Vn|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(C(153));if(e.child!==null){for(t=e.child,n=fn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=fn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Lx(t,e,n){switch(e.tag){case 3:Wm(e),Cr();break;case 5:ym(e);break;case 1:We(e.type)&&la(e);break;case 4:Rc(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,i=e.memoizedProps.value;Z(da,r._currentValue),r._currentValue=i;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(Z(ie,ie.current&1),e.flags|=128,null):n&e.child.childLanes?Vm(t,e,n):(Z(ie,ie.current&1),t=Wt(t,e,n),t!==null?t.sibling:null);Z(ie,ie.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return Ym(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(ie,ie.current),r)break;return null;case 22:case 23:return e.lanes=0,Hm(t,e,n)}return Wt(t,e,n)}var Xm,Dl,Qm,Km;Xm=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Dl=function(){};Qm=function(t,e,n,r){var i=t.memoizedProps;if(i!==r){t=e.stateNode,zn(Pt.current);var s=null;switch(n){case"input":i=nl(t,i),r=nl(t,r),s=[];break;case"select":i=ae({},i,{value:void 0}),r=ae({},r,{value:void 0}),s=[];break;case"textarea":i=sl(t,i),r=sl(t,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=aa)}ol(n,r);var a;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var o=i[u];for(a in o)o.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ni.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(o=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==o&&(l!=null||o!=null))if(u==="style")if(o){for(a in o)!o.hasOwnProperty(a)||l&&l.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in l)l.hasOwnProperty(a)&&o[a]!==l[a]&&(n||(n={}),n[a]=l[a])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,o=o?o.__html:void 0,l!=null&&o!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ni.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ee("scroll",t),s||o===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(e.updateQueue=u)&&(e.flags|=4)}};Km=function(t,e,n,r){n!==r&&(e.flags|=4)};function Qr(t,e){if(!re)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ke(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function zx(t,e,n){var r=e.pendingProps;switch(jc(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ke(e),null;case 1:return We(e.type)&&oa(),ke(e),null;case 3:return r=e.stateNode,Mr(),ne(Be),ne(Me),Oc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(ps(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,ft!==null&&(Hl(ft),ft=null))),Dl(t,e),ke(e),null;case 5:Dc(e);var i=zn(Li.current);if(n=e.type,t!==null&&e.stateNode!=null)Qm(t,e,n,r,i),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(C(166));return ke(e),null}if(t=zn(Pt.current),ps(e)){r=e.stateNode,n=e.type;var s=e.memoizedProps;switch(r[jt]=e,r[Di]=s,t=(e.mode&1)!==0,n){case"dialog":ee("cancel",r),ee("close",r);break;case"iframe":case"object":case"embed":ee("load",r);break;case"video":case"audio":for(i=0;i<ai.length;i++)ee(ai[i],r);break;case"source":ee("error",r);break;case"img":case"image":case"link":ee("error",r),ee("load",r);break;case"details":ee("toggle",r);break;case"input":Su(r,s),ee("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},ee("invalid",r);break;case"textarea":Nu(r,s),ee("invalid",r)}ol(n,s),i=null;for(var a in s)if(s.hasOwnProperty(a)){var o=s[a];a==="children"?typeof o=="string"?r.textContent!==o&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,o,t),i=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(s.suppressHydrationWarning!==!0&&ms(r.textContent,o,t),i=["children",""+o]):Ni.hasOwnProperty(a)&&o!=null&&a==="onScroll"&&ee("scroll",r)}switch(n){case"input":as(r),_u(r,s,!0);break;case"textarea":as(r),ku(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=aa)}r=i,e.updateQueue=r,r!==null&&(e.flags|=4)}else{a=i.nodeType===9?i:i.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=_f(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=a.createElement(n,{is:r.is}):(t=a.createElement(n),n==="select"&&(a=t,r.multiple?a.multiple=!0:r.size&&(a.size=r.size))):t=a.createElementNS(t,n),t[jt]=e,t[Di]=r,Xm(t,e,!1,!1),e.stateNode=t;e:{switch(a=ll(n,r),n){case"dialog":ee("cancel",t),ee("close",t),i=r;break;case"iframe":case"object":case"embed":ee("load",t),i=r;break;case"video":case"audio":for(i=0;i<ai.length;i++)ee(ai[i],t);i=r;break;case"source":ee("error",t),i=r;break;case"img":case"image":case"link":ee("error",t),ee("load",t),i=r;break;case"details":ee("toggle",t),i=r;break;case"input":Su(t,r),i=nl(t,r),ee("invalid",t);break;case"option":i=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},i=ae({},r,{value:void 0}),ee("invalid",t);break;case"textarea":Nu(t,r),i=sl(t,r),ee("invalid",t);break;default:i=r}ol(n,i),o=i;for(s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="style"?jf(t,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Nf(t,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ki(t,l):typeof l=="number"&&ki(t,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ni.hasOwnProperty(s)?l!=null&&s==="onScroll"&&ee("scroll",t):l!=null&&dc(t,s,l,a))}switch(n){case"input":as(t),_u(t,r,!1);break;case"textarea":as(t),ku(t);break;case"option":r.value!=null&&t.setAttribute("value",""+yn(r.value));break;case"select":t.multiple=!!r.multiple,s=r.value,s!=null?pr(t,!!r.multiple,s,!1):r.defaultValue!=null&&pr(t,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(t.onclick=aa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return ke(e),null;case 6:if(t&&e.stateNode!=null)Km(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(C(166));if(n=zn(Li.current),zn(Pt.current),ps(e)){if(r=e.stateNode,n=e.memoizedProps,r[jt]=e,(s=r.nodeValue!==n)&&(t=Ge,t!==null))switch(t.tag){case 3:ms(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&ms(r.nodeValue,n,(t.mode&1)!==0)}s&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[jt]=e,e.stateNode=r}return ke(e),null;case 13:if(ne(ie),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(re&&Ke!==null&&e.mode&1&&!(e.flags&128))fm(),Cr(),e.flags|=98560,s=!1;else if(s=ps(e),r!==null&&r.dehydrated!==null){if(t===null){if(!s)throw Error(C(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(C(317));s[jt]=e}else Cr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;ke(e),s=!1}else ft!==null&&(Hl(ft),ft=null),s=!0;if(!s)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||ie.current&1?xe===0&&(xe=3):Xc())),e.updateQueue!==null&&(e.flags|=4),ke(e),null);case 4:return Mr(),Dl(t,e),t===null&&Ti(e.stateNode.containerInfo),ke(e),null;case 10:return $c(e.type._context),ke(e),null;case 17:return We(e.type)&&oa(),ke(e),null;case 19:if(ne(ie),s=e.memoizedState,s===null)return ke(e),null;if(r=(e.flags&128)!==0,a=s.rendering,a===null)if(r)Qr(s,!1);else{if(xe!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(a=ma(t),a!==null){for(e.flags|=128,Qr(s,!1),r=a.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)s=n,t=r,s.flags&=14680066,a=s.alternate,a===null?(s.childLanes=0,s.lanes=t,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=a.childLanes,s.lanes=a.lanes,s.child=a.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=a.memoizedProps,s.memoizedState=a.memoizedState,s.updateQueue=a.updateQueue,s.type=a.type,t=a.dependencies,s.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return Z(ie,ie.current&1|2),e.child}t=t.sibling}s.tail!==null&&ce()>Er&&(e.flags|=128,r=!0,Qr(s,!1),e.lanes=4194304)}else{if(!r)if(t=ma(a),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),Qr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!a.alternate&&!re)return ke(e),null}else 2*ce()-s.renderingStartTime>Er&&n!==1073741824&&(e.flags|=128,r=!0,Qr(s,!1),e.lanes=4194304);s.isBackwards?(a.sibling=e.child,e.child=a):(n=s.last,n!==null?n.sibling=a:e.child=a,s.last=a)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=ce(),e.sibling=null,n=ie.current,Z(ie,r?n&1|2:n&1),e):(ke(e),null);case 22:case 23:return Yc(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?Qe&1073741824&&(ke(e),e.subtreeFlags&6&&(e.flags|=8192)):ke(e),null;case 24:return null;case 25:return null}throw Error(C(156,e.tag))}function Fx(t,e){switch(jc(e),e.tag){case 1:return We(e.type)&&oa(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Mr(),ne(Be),ne(Me),Oc(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return Dc(e),null;case 13:if(ne(ie),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(C(340));Cr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return ne(ie),null;case 4:return Mr(),null;case 10:return $c(e.type._context),null;case 22:case 23:return Yc(),null;case 24:return null;default:return null}}var ys=!1,Ce=!1,Ax=typeof WeakSet=="function"?WeakSet:Set,D=null;function fr(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){oe(t,e,r)}else n.current=null}function Ol(t,e,n){try{n()}catch(r){oe(t,e,r)}}var md=!1;function Ix(t,e){if(yl=ra,t=em(),Nc(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var a=0,o=-1,l=-1,u=0,d=0,h=t,f=null;t:for(;;){for(var m;h!==n||i!==0&&h.nodeType!==3||(o=a+i),h!==s||r!==0&&h.nodeType!==3||(l=a+r),h.nodeType===3&&(a+=h.nodeValue.length),(m=h.firstChild)!==null;)f=h,h=m;for(;;){if(h===t)break t;if(f===n&&++u===i&&(o=a),f===s&&++d===r&&(l=a),(m=h.nextSibling)!==null)break;h=f,f=h.parentNode}h=m}n=o===-1||l===-1?null:{start:o,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(vl={focusedElem:t,selectionRange:n},ra=!1,D=e;D!==null;)if(e=D,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,D=t;else for(;D!==null;){e=D;try{var x=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var y=x.memoizedProps,b=x.memoizedState,g=e.stateNode,p=g.getSnapshotBeforeUpdate(e.elementType===e.type?y:dt(e.type,y),b);g.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var v=e.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){oe(e,e.return,w)}if(t=e.sibling,t!==null){t.return=e.return,D=t;break}D=e.return}return x=md,md=!1,x}function yi(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&t)===t){var s=i.destroy;i.destroy=void 0,s!==void 0&&Ol(e,n,s)}i=i.next}while(i!==r)}}function Qa(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Ll(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function Gm(t){var e=t.alternate;e!==null&&(t.alternate=null,Gm(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[jt],delete e[Di],delete e[Sl],delete e[Sx],delete e[_x])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function qm(t){return t.tag===5||t.tag===3||t.tag===4}function pd(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||qm(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zl(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=aa));else if(r!==4&&(t=t.child,t!==null))for(zl(t,e,n),t=t.sibling;t!==null;)zl(t,e,n),t=t.sibling}function Fl(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Fl(t,e,n),t=t.sibling;t!==null;)Fl(t,e,n),t=t.sibling}var we=null,ht=!1;function Xt(t,e,n){for(n=n.child;n!==null;)Zm(t,e,n),n=n.sibling}function Zm(t,e,n){if(Ct&&typeof Ct.onCommitFiberUnmount=="function")try{Ct.onCommitFiberUnmount(Ia,n)}catch{}switch(n.tag){case 5:Ce||fr(n,e);case 6:var r=we,i=ht;we=null,Xt(t,e,n),we=r,ht=i,we!==null&&(ht?(t=we,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):we.removeChild(n.stateNode));break;case 18:we!==null&&(ht?(t=we,n=n.stateNode,t.nodeType===8?jo(t.parentNode,n):t.nodeType===1&&jo(t,n),Mi(t)):jo(we,n.stateNode));break;case 4:r=we,i=ht,we=n.stateNode.containerInfo,ht=!0,Xt(t,e,n),we=r,ht=i;break;case 0:case 11:case 14:case 15:if(!Ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,a=s.destroy;s=s.tag,a!==void 0&&(s&2||s&4)&&Ol(n,e,a),i=i.next}while(i!==r)}Xt(t,e,n);break;case 1:if(!Ce&&(fr(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){oe(n,e,o)}Xt(t,e,n);break;case 21:Xt(t,e,n);break;case 22:n.mode&1?(Ce=(r=Ce)||n.memoizedState!==null,Xt(t,e,n),Ce=r):Xt(t,e,n);break;default:Xt(t,e,n)}}function gd(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Ax),e.forEach(function(r){var i=Kx.bind(null,t,r);n.has(r)||(n.add(r),r.then(i,i))})}}function ut(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=t,a=e,o=a;e:for(;o!==null;){switch(o.tag){case 5:we=o.stateNode,ht=!1;break e;case 3:we=o.stateNode.containerInfo,ht=!0;break e;case 4:we=o.stateNode.containerInfo,ht=!0;break e}o=o.return}if(we===null)throw Error(C(160));Zm(s,a,i),we=null,ht=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){oe(i,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)Jm(e,t),e=e.sibling}function Jm(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(ut(e,t),wt(t),r&4){try{yi(3,t,t.return),Qa(3,t)}catch(y){oe(t,t.return,y)}try{yi(5,t,t.return)}catch(y){oe(t,t.return,y)}}break;case 1:ut(e,t),wt(t),r&512&&n!==null&&fr(n,n.return);break;case 5:if(ut(e,t),wt(t),r&512&&n!==null&&fr(n,n.return),t.flags&32){var i=t.stateNode;try{ki(i,"")}catch(y){oe(t,t.return,y)}}if(r&4&&(i=t.stateNode,i!=null)){var s=t.memoizedProps,a=n!==null?n.memoizedProps:s,o=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{o==="input"&&s.type==="radio"&&s.name!=null&&wf(i,s),ll(o,a);var u=ll(o,s);for(a=0;a<l.length;a+=2){var d=l[a],h=l[a+1];d==="style"?jf(i,h):d==="dangerouslySetInnerHTML"?Nf(i,h):d==="children"?ki(i,h):dc(i,d,h,u)}switch(o){case"input":rl(i,s);break;case"textarea":Sf(i,s);break;case"select":var f=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var m=s.value;m!=null?pr(i,!!s.multiple,m,!1):f!==!!s.multiple&&(s.defaultValue!=null?pr(i,!!s.multiple,s.defaultValue,!0):pr(i,!!s.multiple,s.multiple?[]:"",!1))}i[Di]=s}catch(y){oe(t,t.return,y)}}break;case 6:if(ut(e,t),wt(t),r&4){if(t.stateNode===null)throw Error(C(162));i=t.stateNode,s=t.memoizedProps;try{i.nodeValue=s}catch(y){oe(t,t.return,y)}}break;case 3:if(ut(e,t),wt(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Mi(e.containerInfo)}catch(y){oe(t,t.return,y)}break;case 4:ut(e,t),wt(t);break;case 13:ut(e,t),wt(t),i=t.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(Wc=ce())),r&4&&gd(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(Ce=(u=Ce)||d,ut(e,t),Ce=u):ut(e,t),wt(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(D=t,d=t.child;d!==null;){for(h=D=d;D!==null;){switch(f=D,m=f.child,f.tag){case 0:case 11:case 14:case 15:yi(4,f,f.return);break;case 1:fr(f,f.return);var x=f.stateNode;if(typeof x.componentWillUnmount=="function"){r=f,n=f.return;try{e=r,x.props=e.memoizedProps,x.state=e.memoizedState,x.componentWillUnmount()}catch(y){oe(r,n,y)}}break;case 5:fr(f,f.return);break;case 22:if(f.memoizedState!==null){yd(h);continue}}m!==null?(m.return=f,D=m):yd(h)}d=d.sibling}e:for(d=null,h=t;;){if(h.tag===5){if(d===null){d=h;try{i=h.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(o=h.stateNode,l=h.memoizedProps.style,a=l!=null&&l.hasOwnProperty("display")?l.display:null,o.style.display=kf("display",a))}catch(y){oe(t,t.return,y)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(y){oe(t,t.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===t)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===t)break e;for(;h.sibling===null;){if(h.return===null||h.return===t)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ut(e,t),wt(t),r&4&&gd(t);break;case 21:break;default:ut(e,t),wt(t)}}function wt(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(qm(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(ki(i,""),r.flags&=-33);var s=pd(t);Fl(t,s,i);break;case 3:case 4:var a=r.stateNode.containerInfo,o=pd(t);zl(t,o,a);break;default:throw Error(C(161))}}catch(l){oe(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ux(t,e,n){D=t,ep(t)}function ep(t,e,n){for(var r=(t.mode&1)!==0;D!==null;){var i=D,s=i.child;if(i.tag===22&&r){var a=i.memoizedState!==null||ys;if(!a){var o=i.alternate,l=o!==null&&o.memoizedState!==null||Ce;o=ys;var u=Ce;if(ys=a,(Ce=l)&&!u)for(D=i;D!==null;)a=D,l=a.child,a.tag===22&&a.memoizedState!==null?vd(i):l!==null?(l.return=a,D=l):vd(i);for(;s!==null;)D=s,ep(s),s=s.sibling;D=i,ys=o,Ce=u}xd(t)}else i.subtreeFlags&8772&&s!==null?(s.return=i,D=s):xd(t)}}function xd(t){for(;D!==null;){var e=D;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:Ce||Qa(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!Ce)if(n===null)r.componentDidMount();else{var i=e.elementType===e.type?n.memoizedProps:dt(e.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=e.updateQueue;s!==null&&td(e,s,r);break;case 3:var a=e.updateQueue;if(a!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}td(e,a,n)}break;case 5:var o=e.stateNode;if(n===null&&e.flags&4){n=o;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Mi(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}Ce||e.flags&512&&Ll(e)}catch(f){oe(e,e.return,f)}}if(e===t){D=null;break}if(n=e.sibling,n!==null){n.return=e.return,D=n;break}D=e.return}}function yd(t){for(;D!==null;){var e=D;if(e===t){D=null;break}var n=e.sibling;if(n!==null){n.return=e.return,D=n;break}D=e.return}}function vd(t){for(;D!==null;){var e=D;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{Qa(4,e)}catch(l){oe(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var i=e.return;try{r.componentDidMount()}catch(l){oe(e,i,l)}}var s=e.return;try{Ll(e)}catch(l){oe(e,s,l)}break;case 5:var a=e.return;try{Ll(e)}catch(l){oe(e,a,l)}}}catch(l){oe(e,e.return,l)}if(e===t){D=null;break}var o=e.sibling;if(o!==null){o.return=e.return,D=o;break}D=e.return}}var Hx=Math.ceil,xa=Yt.ReactCurrentDispatcher,Hc=Yt.ReactCurrentOwner,st=Yt.ReactCurrentBatchConfig,H=0,be=null,de=null,Se=0,Qe=0,mr=Sn(0),xe=0,Ii=null,Vn=0,Ka=0,Bc=0,vi=null,Ae=null,Wc=0,Er=1/0,Dt=null,ya=!1,Al=null,dn=null,vs=!1,tn=null,va=0,bi=0,Il=null,Ws=-1,Vs=0;function De(){return H&6?ce():Ws!==-1?Ws:Ws=ce()}function hn(t){return t.mode&1?H&2&&Se!==0?Se&-Se:kx.transition!==null?(Vs===0&&(Vs=Ff()),Vs):(t=Q,t!==0||(t=window.event,t=t===void 0?16:Vf(t.type)),t):1}function yt(t,e,n,r){if(50<bi)throw bi=0,Il=null,Error(C(185));Gi(t,n,r),(!(H&2)||t!==be)&&(t===be&&(!(H&2)&&(Ka|=n),xe===4&&Jt(t,Se)),Ve(t,r),n===1&&H===0&&!(e.mode&1)&&(Er=ce()+500,Va&&_n()))}function Ve(t,e){var n=t.callbackNode;k0(t,e);var r=na(t,t===be?Se:0);if(r===0)n!==null&&Pu(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&Pu(n),e===1)t.tag===0?Nx(bd.bind(null,t)):um(bd.bind(null,t)),bx(function(){!(H&6)&&_n()}),n=null;else{switch(Af(r)){case 1:n=gc;break;case 4:n=Lf;break;case 16:n=ta;break;case 536870912:n=zf;break;default:n=ta}n=lp(n,tp.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function tp(t,e){if(Ws=-1,Vs=0,H&6)throw Error(C(327));var n=t.callbackNode;if(br()&&t.callbackNode!==n)return null;var r=na(t,t===be?Se:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=ba(t,r);else{e=r;var i=H;H|=2;var s=rp();(be!==t||Se!==e)&&(Dt=null,Er=ce()+500,An(t,e));do try{Vx();break}catch(o){np(t,o)}while(!0);Mc(),xa.current=s,H=i,de!==null?e=0:(be=null,Se=0,e=xe)}if(e!==0){if(e===2&&(i=fl(t),i!==0&&(r=i,e=Ul(t,i))),e===1)throw n=Ii,An(t,0),Jt(t,r),Ve(t,ce()),n;if(e===6)Jt(t,r);else{if(i=t.current.alternate,!(r&30)&&!Bx(i)&&(e=ba(t,r),e===2&&(s=fl(t),s!==0&&(r=s,e=Ul(t,s))),e===1))throw n=Ii,An(t,0),Jt(t,r),Ve(t,ce()),n;switch(t.finishedWork=i,t.finishedLanes=r,e){case 0:case 1:throw Error(C(345));case 2:En(t,Ae,Dt);break;case 3:if(Jt(t,r),(r&130023424)===r&&(e=Wc+500-ce(),10<e)){if(na(t,0)!==0)break;if(i=t.suspendedLanes,(i&r)!==r){De(),t.pingedLanes|=t.suspendedLanes&i;break}t.timeoutHandle=wl(En.bind(null,t,Ae,Dt),e);break}En(t,Ae,Dt);break;case 4:if(Jt(t,r),(r&4194240)===r)break;for(e=t.eventTimes,i=-1;0<r;){var a=31-xt(r);s=1<<a,a=e[a],a>i&&(i=a),r&=~s}if(r=i,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Hx(r/1960))-r,10<r){t.timeoutHandle=wl(En.bind(null,t,Ae,Dt),r);break}En(t,Ae,Dt);break;case 5:En(t,Ae,Dt);break;default:throw Error(C(329))}}}return Ve(t,ce()),t.callbackNode===n?tp.bind(null,t):null}function Ul(t,e){var n=vi;return t.current.memoizedState.isDehydrated&&(An(t,e).flags|=256),t=ba(t,e),t!==2&&(e=Ae,Ae=n,e!==null&&Hl(e)),t}function Hl(t){Ae===null?Ae=t:Ae.push.apply(Ae,t)}function Bx(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!vt(s(),i))return!1}catch{return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Jt(t,e){for(e&=~Bc,e&=~Ka,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-xt(e),r=1<<n;t[n]=-1,e&=~r}}function bd(t){if(H&6)throw Error(C(327));br();var e=na(t,0);if(!(e&1))return Ve(t,ce()),null;var n=ba(t,e);if(t.tag!==0&&n===2){var r=fl(t);r!==0&&(e=r,n=Ul(t,r))}if(n===1)throw n=Ii,An(t,0),Jt(t,e),Ve(t,ce()),n;if(n===6)throw Error(C(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,En(t,Ae,Dt),Ve(t,ce()),null}function Vc(t,e){var n=H;H|=1;try{return t(e)}finally{H=n,H===0&&(Er=ce()+500,Va&&_n())}}function Yn(t){tn!==null&&tn.tag===0&&!(H&6)&&br();var e=H;H|=1;var n=st.transition,r=Q;try{if(st.transition=null,Q=1,t)return t()}finally{Q=r,st.transition=n,H=e,!(H&6)&&_n()}}function Yc(){Qe=mr.current,ne(mr)}function An(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,vx(n)),de!==null)for(n=de.return;n!==null;){var r=n;switch(jc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&oa();break;case 3:Mr(),ne(Be),ne(Me),Oc();break;case 5:Dc(r);break;case 4:Mr();break;case 13:ne(ie);break;case 19:ne(ie);break;case 10:$c(r.type._context);break;case 22:case 23:Yc()}n=n.return}if(be=t,de=t=fn(t.current,null),Se=Qe=e,xe=0,Ii=null,Bc=Ka=Vn=0,Ae=vi=null,Ln!==null){for(e=0;e<Ln.length;e++)if(n=Ln[e],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var a=s.next;s.next=i,r.next=a}n.pending=r}Ln=null}return t}function np(t,e){do{var n=de;try{if(Mc(),Us.current=ga,pa){for(var r=se.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}pa=!1}if(Wn=0,ve=pe=se=null,xi=!1,zi=0,Hc.current=null,n===null||n.return===null){xe=1,Ii=e,de=null;break}e:{var s=t,a=n.return,o=n,l=e;if(e=Se,o.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=o,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var m=od(a);if(m!==null){m.flags&=-257,ld(m,a,o,s,e),m.mode&1&&ad(s,u,e),e=m,l=u;var x=e.updateQueue;if(x===null){var y=new Set;y.add(l),e.updateQueue=y}else x.add(l);break e}else{if(!(e&1)){ad(s,u,e),Xc();break e}l=Error(C(426))}}else if(re&&o.mode&1){var b=od(a);if(b!==null){!(b.flags&65536)&&(b.flags|=256),ld(b,a,o,s,e),Cc($r(l,o));break e}}s=l=$r(l,o),xe!==4&&(xe=2),vi===null?vi=[s]:vi.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,e&=-e,s.lanes|=e;var g=Am(s,l,e);ed(s,g);break e;case 1:o=l;var p=s.type,v=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(dn===null||!dn.has(v)))){s.flags|=65536,e&=-e,s.lanes|=e;var w=Im(s,o,e);ed(s,w);break e}}s=s.return}while(s!==null)}sp(n)}catch(_){e=_,de===n&&n!==null&&(de=n=n.return);continue}break}while(!0)}function rp(){var t=xa.current;return xa.current=ga,t===null?ga:t}function Xc(){(xe===0||xe===3||xe===2)&&(xe=4),be===null||!(Vn&268435455)&&!(Ka&268435455)||Jt(be,Se)}function ba(t,e){var n=H;H|=2;var r=rp();(be!==t||Se!==e)&&(Dt=null,An(t,e));do try{Wx();break}catch(i){np(t,i)}while(!0);if(Mc(),H=n,xa.current=r,de!==null)throw Error(C(261));return be=null,Se=0,xe}function Wx(){for(;de!==null;)ip(de)}function Vx(){for(;de!==null&&!g0();)ip(de)}function ip(t){var e=op(t.alternate,t,Qe);t.memoizedProps=t.pendingProps,e===null?sp(t):de=e,Hc.current=null}function sp(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Fx(n,e),n!==null){n.flags&=32767,de=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{xe=6,de=null;return}}else if(n=zx(n,e,Qe),n!==null){de=n;return}if(e=e.sibling,e!==null){de=e;return}de=e=t}while(e!==null);xe===0&&(xe=5)}function En(t,e,n){var r=Q,i=st.transition;try{st.transition=null,Q=1,Yx(t,e,n,r)}finally{st.transition=i,Q=r}return null}function Yx(t,e,n,r){do br();while(tn!==null);if(H&6)throw Error(C(327));n=t.finishedWork;var i=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(C(177));t.callbackNode=null,t.callbackPriority=0;var s=n.lanes|n.childLanes;if(j0(t,s),t===be&&(de=be=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||vs||(vs=!0,lp(ta,function(){return br(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=st.transition,st.transition=null;var a=Q;Q=1;var o=H;H|=4,Hc.current=null,Ix(t,n),Jm(n,t),hx(vl),ra=!!yl,vl=yl=null,t.current=n,Ux(n),x0(),H=o,Q=a,st.transition=s}else t.current=n;if(vs&&(vs=!1,tn=t,va=i),s=t.pendingLanes,s===0&&(dn=null),b0(n.stateNode),Ve(t,ce()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)i=e[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ya)throw ya=!1,t=Al,Al=null,t;return va&1&&t.tag!==0&&br(),s=t.pendingLanes,s&1?t===Il?bi++:(bi=0,Il=t):bi=0,_n(),null}function br(){if(tn!==null){var t=Af(va),e=st.transition,n=Q;try{if(st.transition=null,Q=16>t?16:t,tn===null)var r=!1;else{if(t=tn,tn=null,va=0,H&6)throw Error(C(331));var i=H;for(H|=4,D=t.current;D!==null;){var s=D,a=s.child;if(D.flags&16){var o=s.deletions;if(o!==null){for(var l=0;l<o.length;l++){var u=o[l];for(D=u;D!==null;){var d=D;switch(d.tag){case 0:case 11:case 15:yi(8,d,s)}var h=d.child;if(h!==null)h.return=d,D=h;else for(;D!==null;){d=D;var f=d.sibling,m=d.return;if(Gm(d),d===u){D=null;break}if(f!==null){f.return=m,D=f;break}D=m}}}var x=s.alternate;if(x!==null){var y=x.child;if(y!==null){x.child=null;do{var b=y.sibling;y.sibling=null,y=b}while(y!==null)}}D=s}}if(s.subtreeFlags&2064&&a!==null)a.return=s,D=a;else e:for(;D!==null;){if(s=D,s.flags&2048)switch(s.tag){case 0:case 11:case 15:yi(9,s,s.return)}var g=s.sibling;if(g!==null){g.return=s.return,D=g;break e}D=s.return}}var p=t.current;for(D=p;D!==null;){a=D;var v=a.child;if(a.subtreeFlags&2064&&v!==null)v.return=a,D=v;else e:for(a=p;D!==null;){if(o=D,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Qa(9,o)}}catch(_){oe(o,o.return,_)}if(o===a){D=null;break e}var w=o.sibling;if(w!==null){w.return=o.return,D=w;break e}D=o.return}}if(H=i,_n(),Ct&&typeof Ct.onPostCommitFiberRoot=="function")try{Ct.onPostCommitFiberRoot(Ia,t)}catch{}r=!0}return r}finally{Q=n,st.transition=e}}return!1}function wd(t,e,n){e=$r(n,e),e=Am(t,e,1),t=un(t,e,1),e=De(),t!==null&&(Gi(t,1,e),Ve(t,e))}function oe(t,e,n){if(t.tag===3)wd(t,t,n);else for(;e!==null;){if(e.tag===3){wd(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(dn===null||!dn.has(r))){t=$r(n,t),t=Im(e,t,1),e=un(e,t,1),t=De(),e!==null&&(Gi(e,1,t),Ve(e,t));break}}e=e.return}}function Xx(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=De(),t.pingedLanes|=t.suspendedLanes&n,be===t&&(Se&n)===n&&(xe===4||xe===3&&(Se&130023424)===Se&&500>ce()-Wc?An(t,0):Bc|=n),Ve(t,e)}function ap(t,e){e===0&&(t.mode&1?(e=cs,cs<<=1,!(cs&130023424)&&(cs=4194304)):e=1);var n=De();t=Bt(t,e),t!==null&&(Gi(t,e,n),Ve(t,n))}function Qx(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),ap(t,n)}function Kx(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(e),ap(t,n)}var op;op=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Be.current)Ue=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return Ue=!1,Lx(t,e,n);Ue=!!(t.flags&131072)}else Ue=!1,re&&e.flags&1048576&&dm(e,ua,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;Bs(t,e),t=e.pendingProps;var i=jr(e,Me.current);vr(e,n),i=zc(null,e,r,t,i,n);var s=Fc();return e.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,We(r)?(s=!0,la(e)):s=!1,e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Tc(e),i.updater=Xa,e.stateNode=i,i._reactInternals=e,Pl(e,r,t,n),e=El(null,e,r,!0,s,n)):(e.tag=0,re&&s&&kc(e),Te(null,e,i,n),e=e.child),e;case 16:r=e.elementType;e:{switch(Bs(t,e),t=e.pendingProps,i=r._init,r=i(r._payload),e.type=r,i=e.tag=qx(r),t=dt(r,t),i){case 0:e=$l(null,e,r,t,n);break e;case 1:e=dd(null,e,r,t,n);break e;case 11:e=cd(null,e,r,t,n);break e;case 14:e=ud(null,e,r,dt(r.type,t),n);break e}throw Error(C(306,r,""))}return e;case 0:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),$l(t,e,r,i,n);case 1:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),dd(t,e,r,i,n);case 3:e:{if(Wm(e),t===null)throw Error(C(387));r=e.pendingProps,s=e.memoizedState,i=s.element,xm(t,e),fa(e,r,null,n);var a=e.memoizedState;if(r=a.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){i=$r(Error(C(423)),e),e=hd(t,e,r,n,i);break e}else if(r!==i){i=$r(Error(C(424)),e),e=hd(t,e,r,n,i);break e}else for(Ke=cn(e.stateNode.containerInfo.firstChild),Ge=e,re=!0,ft=null,n=pm(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Cr(),r===i){e=Wt(t,e,n);break e}Te(t,e,r,n)}e=e.child}return e;case 5:return ym(e),t===null&&kl(e),r=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,a=i.children,bl(r,i)?a=null:s!==null&&bl(r,s)&&(e.flags|=32),Bm(t,e),Te(t,e,a,n),e.child;case 6:return t===null&&kl(e),null;case 13:return Vm(t,e,n);case 4:return Rc(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Pr(e,null,r,n):Te(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),cd(t,e,r,i,n);case 7:return Te(t,e,e.pendingProps,n),e.child;case 8:return Te(t,e,e.pendingProps.children,n),e.child;case 12:return Te(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,i=e.pendingProps,s=e.memoizedProps,a=i.value,Z(da,r._currentValue),r._currentValue=a,s!==null)if(vt(s.value,a)){if(s.children===i.children&&!Be.current){e=Wt(t,e,n);break e}}else for(s=e.child,s!==null&&(s.return=e);s!==null;){var o=s.dependencies;if(o!==null){a=s.child;for(var l=o.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=It(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),jl(s.return,n,e),o.lanes|=n;break}l=l.next}}else if(s.tag===10)a=s.type===e.type?null:s.child;else if(s.tag===18){if(a=s.return,a===null)throw Error(C(341));a.lanes|=n,o=a.alternate,o!==null&&(o.lanes|=n),jl(a,n,e),a=s.sibling}else a=s.child;if(a!==null)a.return=s;else for(a=s;a!==null;){if(a===e){a=null;break}if(s=a.sibling,s!==null){s.return=a.return,a=s;break}a=a.return}s=a}Te(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=e.pendingProps.children,vr(e,n),i=at(i),r=r(i),e.flags|=1,Te(t,e,r,n),e.child;case 14:return r=e.type,i=dt(r,e.pendingProps),i=dt(r.type,i),ud(t,e,r,i,n);case 15:return Um(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:dt(r,i),Bs(t,e),e.tag=1,We(r)?(t=!0,la(e)):t=!1,vr(e,n),Fm(e,r,i),Pl(e,r,i,n),El(null,e,r,!0,t,n);case 19:return Ym(t,e,n);case 22:return Hm(t,e,n)}throw Error(C(156,e.tag))};function lp(t,e){return Of(t,e)}function Gx(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rt(t,e,n,r){return new Gx(t,e,n,r)}function Qc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function qx(t){if(typeof t=="function")return Qc(t)?1:0;if(t!=null){if(t=t.$$typeof,t===fc)return 11;if(t===mc)return 14}return 2}function fn(t,e){var n=t.alternate;return n===null?(n=rt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Ys(t,e,n,r,i,s){var a=2;if(r=t,typeof t=="function")Qc(t)&&(a=1);else if(typeof t=="string")a=5;else e:switch(t){case ir:return In(n.children,i,s,e);case hc:a=8,i|=8;break;case Zo:return t=rt(12,n,e,i|2),t.elementType=Zo,t.lanes=s,t;case Jo:return t=rt(13,n,e,i),t.elementType=Jo,t.lanes=s,t;case el:return t=rt(19,n,e,i),t.elementType=el,t.lanes=s,t;case yf:return Ga(n,i,s,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case gf:a=10;break e;case xf:a=9;break e;case fc:a=11;break e;case mc:a=14;break e;case Gt:a=16,r=null;break e}throw Error(C(130,t==null?t:typeof t,""))}return e=rt(a,n,e,i),e.elementType=t,e.type=r,e.lanes=s,e}function In(t,e,n,r){return t=rt(7,t,r,e),t.lanes=n,t}function Ga(t,e,n,r){return t=rt(22,t,r,e),t.elementType=yf,t.lanes=n,t.stateNode={isHidden:!1},t}function Do(t,e,n){return t=rt(6,t,null,e),t.lanes=n,t}function Oo(t,e,n){return e=rt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Zx(t,e,n,r,i){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=po(0),this.expirationTimes=po(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=po(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Kc(t,e,n,r,i,s,a,o,l){return t=new Zx(t,e,n,o,l),e===1?(e=1,s===!0&&(e|=8)):e=0,s=rt(3,null,null,e),t.current=s,s.stateNode=t,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Tc(s),t}function Jx(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:rr,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function cp(t){if(!t)return vn;t=t._reactInternals;e:{if(qn(t)!==t||t.tag!==1)throw Error(C(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(We(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(C(171))}if(t.tag===1){var n=t.type;if(We(n))return cm(t,n,e)}return e}function up(t,e,n,r,i,s,a,o,l){return t=Kc(n,r,!0,t,i,s,a,o,l),t.context=cp(null),n=t.current,r=De(),i=hn(n),s=It(r,i),s.callback=e??null,un(n,s,i),t.current.lanes=i,Gi(t,i,r),Ve(t,r),t}function qa(t,e,n,r){var i=e.current,s=De(),a=hn(i);return n=cp(n),e.context===null?e.context=n:e.pendingContext=n,e=It(s,a),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=un(i,e,a),t!==null&&(yt(t,i,a,s),Is(t,i,a)),a}function wa(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function Gc(t,e){Sd(t,e),(t=t.alternate)&&Sd(t,e)}function ey(){return null}var dp=typeof reportError=="function"?reportError:function(t){console.error(t)};function qc(t){this._internalRoot=t}Za.prototype.render=qc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(C(409));qa(t,e,null,null)};Za.prototype.unmount=qc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Yn(function(){qa(null,t,null,null)}),e[Ht]=null}};function Za(t){this._internalRoot=t}Za.prototype.unstable_scheduleHydration=function(t){if(t){var e=Hf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Zt.length&&e!==0&&e<Zt[n].priority;n++);Zt.splice(n,0,t),n===0&&Wf(t)}};function Zc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Ja(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function _d(){}function ty(t,e,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=wa(a);s.call(u)}}var a=up(e,r,t,0,null,!1,!1,"",_d);return t._reactRootContainer=a,t[Ht]=a.current,Ti(t.nodeType===8?t.parentNode:t),Yn(),a}for(;i=t.lastChild;)t.removeChild(i);if(typeof r=="function"){var o=r;r=function(){var u=wa(l);o.call(u)}}var l=Kc(t,0,!1,null,null,!1,!1,"",_d);return t._reactRootContainer=l,t[Ht]=l.current,Ti(t.nodeType===8?t.parentNode:t),Yn(function(){qa(e,l,n,r)}),l}function eo(t,e,n,r,i){var s=n._reactRootContainer;if(s){var a=s;if(typeof i=="function"){var o=i;i=function(){var l=wa(a);o.call(l)}}qa(e,a,t,i)}else a=ty(n,e,t,i,r);return wa(a)}If=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=si(e.pendingLanes);n!==0&&(xc(e,n|1),Ve(e,ce()),!(H&6)&&(Er=ce()+500,_n()))}break;case 13:Yn(function(){var r=Bt(t,1);if(r!==null){var i=De();yt(r,t,1,i)}}),Gc(t,1)}};yc=function(t){if(t.tag===13){var e=Bt(t,134217728);if(e!==null){var n=De();yt(e,t,134217728,n)}Gc(t,134217728)}};Uf=function(t){if(t.tag===13){var e=hn(t),n=Bt(t,e);if(n!==null){var r=De();yt(n,t,e,r)}Gc(t,e)}};Hf=function(){return Q};Bf=function(t,e){var n=Q;try{return Q=t,e()}finally{Q=n}};ul=function(t,e,n){switch(e){case"input":if(rl(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Wa(r);if(!i)throw Error(C(90));bf(r),rl(r,i)}}}break;case"textarea":Sf(t,n);break;case"select":e=n.value,e!=null&&pr(t,!!n.multiple,e,!1)}};Mf=Vc;$f=Yn;var ny={usingClientEntryPoint:!1,Events:[Zi,lr,Wa,Cf,Pf,Vc]},Kr={findFiberByHostInstance:On,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ry={bundleType:Kr.bundleType,version:Kr.version,rendererPackageName:Kr.rendererPackageName,rendererConfig:Kr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Yt.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Rf(t),t===null?null:t.stateNode},findFiberByHostInstance:Kr.findFiberByHostInstance||ey,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bs.isDisabled&&bs.supportsFiber)try{Ia=bs.inject(ry),Ct=bs}catch{}}Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ny;Ze.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Zc(e))throw Error(C(200));return Jx(t,e,null,n)};Ze.createRoot=function(t,e){if(!Zc(t))throw Error(C(299));var n=!1,r="",i=dp;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(i=e.onRecoverableError)),e=Kc(t,1,!1,null,null,n,!1,r,i),t[Ht]=e.current,Ti(t.nodeType===8?t.parentNode:t),new qc(e)};Ze.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(C(188)):(t=Object.keys(t).join(","),Error(C(268,t)));return t=Rf(e),t=t===null?null:t.stateNode,t};Ze.flushSync=function(t){return Yn(t)};Ze.hydrate=function(t,e,n){if(!Ja(e))throw Error(C(200));return eo(null,t,e,!0,n)};Ze.hydrateRoot=function(t,e,n){if(!Zc(t))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",a=dp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(a=n.onRecoverableError)),e=up(e,null,t,1,n??null,i,!1,s,a),t[Ht]=e.current,Ti(t),r)for(t=0;t<r.length;t++)n=r[t],i=n._getVersion,i=i(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,i]:e.mutableSourceEagerHydrationData.push(n,i);return new Za(e)};Ze.render=function(t,e,n){if(!Ja(e))throw Error(C(200));return eo(null,t,e,!1,n)};Ze.unmountComponentAtNode=function(t){if(!Ja(t))throw Error(C(40));return t._reactRootContainer?(Yn(function(){eo(null,null,t,!1,function(){t._reactRootContainer=null,t[Ht]=null})}),!0):!1};Ze.unstable_batchedUpdates=Vc;Ze.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!Ja(n))throw Error(C(200));if(t==null||t._reactInternals===void 0)throw Error(C(38));return eo(t,e,n,!1,r)};Ze.version="18.3.1-next-f1338f8080-20240426";function hp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(hp)}catch(t){console.error(t)}}hp(),hf.exports=Ze;var iy=hf.exports,Nd=iy;Go.createRoot=Nd.createRoot,Go.hydrateRoot=Nd.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ui(){return Ui=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ui.apply(this,arguments)}var nn;(function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"})(nn||(nn={}));const kd="popstate";function sy(t){t===void 0&&(t={});function e(r,i){let{pathname:s,search:a,hash:o}=r.location;return Bl("",{pathname:s,search:a,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Sa(i)}return oy(e,n,null,t)}function le(t,e){if(t===!1||t===null||typeof t>"u")throw new Error(e)}function fp(t,e){if(!t){typeof console<"u"&&console.warn(e);try{throw new Error(e)}catch{}}}function ay(){return Math.random().toString(36).substr(2,8)}function jd(t,e){return{usr:t.state,key:t.key,idx:e}}function Bl(t,e,n,r){return n===void 0&&(n=null),Ui({pathname:typeof t=="string"?t:t.pathname,search:"",hash:""},typeof e=="string"?Fr(e):e,{state:n,key:e&&e.key||r||ay()})}function Sa(t){let{pathname:e="/",search:n="",hash:r=""}=t;return n&&n!=="?"&&(e+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Fr(t){let e={};if(t){let n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));let r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}function oy(t,e,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,a=i.history,o=nn.Pop,l=null,u=d();u==null&&(u=0,a.replaceState(Ui({},a.state,{idx:u}),""));function d(){return(a.state||{idx:null}).idx}function h(){o=nn.Pop;let b=d(),g=b==null?null:b-u;u=b,l&&l({action:o,location:y.location,delta:g})}function f(b,g){o=nn.Push;let p=Bl(y.location,b,g);u=d()+1;let v=jd(p,u),w=y.createHref(p);try{a.pushState(v,"",w)}catch(_){if(_ instanceof DOMException&&_.name==="DataCloneError")throw _;i.location.assign(w)}s&&l&&l({action:o,location:y.location,delta:1})}function m(b,g){o=nn.Replace;let p=Bl(y.location,b,g);u=d();let v=jd(p,u),w=y.createHref(p);a.replaceState(v,"",w),s&&l&&l({action:o,location:y.location,delta:0})}function x(b){let g=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof b=="string"?b:Sa(b);return p=p.replace(/ $/,"%20"),le(g,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,g)}let y={get action(){return o},get location(){return t(i,a)},listen(b){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(kd,h),l=b,()=>{i.removeEventListener(kd,h),l=null}},createHref(b){return e(i,b)},createURL:x,encodeLocation(b){let g=x(b);return{pathname:g.pathname,search:g.search,hash:g.hash}},push:f,replace:m,go(b){return a.go(b)}};return y}var Cd;(function(t){t.data="data",t.deferred="deferred",t.redirect="redirect",t.error="error"})(Cd||(Cd={}));function ly(t,e,n){return n===void 0&&(n="/"),cy(t,e,n)}function cy(t,e,n,r){let i=typeof e=="string"?Fr(e):e,s=Tr(i.pathname||"/",n);if(s==null)return null;let a=mp(t);uy(a);let o=null;for(let l=0;o==null&&l<a.length;++l){let u=wy(s);o=vy(a[l],u)}return o}function mp(t,e,n,r){e===void 0&&(e=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,a,o)=>{let l={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:a,route:s};l.relativePath.startsWith("/")&&(le(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=mn([r,l.relativePath]),d=n.concat(l);s.children&&s.children.length>0&&(le(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),mp(s.children,e,d,u)),!(s.path==null&&!s.index)&&e.push({path:u,score:xy(u,s.index),routesMeta:d})};return t.forEach((s,a)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))i(s,a);else for(let l of pp(s.path))i(s,a,l)}),e}function pp(t){let e=t.split("/");if(e.length===0)return[];let[n,...r]=e,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let a=pp(r.join("/")),o=[];return o.push(...a.map(l=>l===""?s:[s,l].join("/"))),i&&o.push(...a),o.map(l=>t.startsWith("/")&&l===""?"/":l)}function uy(t){t.sort((e,n)=>e.score!==n.score?n.score-e.score:yy(e.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const dy=/^:[\w-]+$/,hy=3,fy=2,my=1,py=10,gy=-2,Pd=t=>t==="*";function xy(t,e){let n=t.split("/"),r=n.length;return n.some(Pd)&&(r+=gy),e&&(r+=fy),n.filter(i=>!Pd(i)).reduce((i,s)=>i+(dy.test(s)?hy:s===""?my:py),r)}function yy(t,e){return t.length===e.length&&t.slice(0,-1).every((r,i)=>r===e[i])?t[t.length-1]-e[e.length-1]:0}function vy(t,e,n){let{routesMeta:r}=t,i={},s="/",a=[];for(let o=0;o<r.length;++o){let l=r[o],u=o===r.length-1,d=s==="/"?e:e.slice(s.length)||"/",h=Wl({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),f=l.route;if(!h)return null;Object.assign(i,h.params),a.push({params:i,pathname:mn([s,h.pathname]),pathnameBase:ky(mn([s,h.pathnameBase])),route:f}),h.pathnameBase!=="/"&&(s=mn([s,h.pathnameBase]))}return a}function Wl(t,e){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[n,r]=by(t.path,t.caseSensitive,t.end),i=e.match(n);if(!i)return null;let s=i[0],a=s.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:r.reduce((u,d,h)=>{let{paramName:f,isOptional:m}=d;if(f==="*"){let y=o[h]||"";a=s.slice(0,s.length-y.length).replace(/(.)\/+$/,"$1")}const x=o[h];return m&&!x?u[f]=void 0:u[f]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:a,pattern:t}}function by(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!0),fp(t==="*"||!t.endsWith("*")||t.endsWith("/*"),'Route path "'+t+'" will be treated as if it were '+('"'+t.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+t.replace(/\*$/,"/*")+'".'));let r=[],i="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,o,l)=>(r.push({paramName:o,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(r.push({paramName:"*"}),i+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":t!==""&&t!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,e?void 0:"i"),r]}function wy(t){try{return t.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(e){return fp(!1,'The URL path "'+t+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+e+").")),t}}function Tr(t,e){if(e==="/")return t;if(!t.toLowerCase().startsWith(e.toLowerCase()))return null;let n=e.endsWith("/")?e.length-1:e.length,r=t.charAt(n);return r&&r!=="/"?null:t.slice(n)||"/"}function Sy(t,e){e===void 0&&(e="/");let{pathname:n,search:r="",hash:i=""}=typeof t=="string"?Fr(t):t;return{pathname:n?n.startsWith("/")?n:_y(n,e):e,search:jy(r),hash:Cy(i)}}function _y(t,e){let n=e.replace(/\/+$/,"").split("/");return t.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Lo(t,e,n,r){return"Cannot include a '"+t+"' character in a manually specified "+("`to."+e+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ny(t){return t.filter((e,n)=>n===0||e.route.path&&e.route.path.length>0)}function gp(t,e){let n=Ny(t);return e?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function xp(t,e,n,r){r===void 0&&(r=!1);let i;typeof t=="string"?i=Fr(t):(i=Ui({},t),le(!i.pathname||!i.pathname.includes("?"),Lo("?","pathname","search",i)),le(!i.pathname||!i.pathname.includes("#"),Lo("#","pathname","hash",i)),le(!i.search||!i.search.includes("#"),Lo("#","search","hash",i)));let s=t===""||i.pathname==="",a=s?"/":i.pathname,o;if(a==null)o=n;else{let h=e.length-1;if(!r&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),h-=1;i.pathname=f.join("/")}o=h>=0?e[h]:"/"}let l=Sy(i,o),u=a&&a!=="/"&&a.endsWith("/"),d=(s||a===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const mn=t=>t.join("/").replace(/\/\/+/g,"/"),ky=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),jy=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Cy=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t;function Py(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}const yp=["post","put","patch","delete"];new Set(yp);const My=["get",...yp];new Set(My);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Hi(){return Hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Hi.apply(this,arguments)}const to=k.createContext(null),vp=k.createContext(null),Nn=k.createContext(null),no=k.createContext(null),Zn=k.createContext({outlet:null,matches:[],isDataRoute:!1}),bp=k.createContext(null);function $y(t,e){let{relative:n}=e===void 0?{}:e;es()||le(!1);let{basename:r,navigator:i}=k.useContext(Nn),{hash:s,pathname:a,search:o}=ro(t,{relative:n}),l=a;return r!=="/"&&(l=a==="/"?r:mn([r,a])),i.createHref({pathname:l,search:o,hash:s})}function es(){return k.useContext(no)!=null}function kn(){return es()||le(!1),k.useContext(no).location}function wp(t){k.useContext(Nn).static||k.useLayoutEffect(t)}function Ar(){let{isDataRoute:t}=k.useContext(Zn);return t?By():Ey()}function Ey(){es()||le(!1);let t=k.useContext(to),{basename:e,future:n,navigator:r}=k.useContext(Nn),{matches:i}=k.useContext(Zn),{pathname:s}=kn(),a=JSON.stringify(gp(i,n.v7_relativeSplatPath)),o=k.useRef(!1);return wp(()=>{o.current=!0}),k.useCallback(function(u,d){if(d===void 0&&(d={}),!o.current)return;if(typeof u=="number"){r.go(u);return}let h=xp(u,JSON.parse(a),s,d.relative==="path");t==null&&e!=="/"&&(h.pathname=h.pathname==="/"?e:mn([e,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[e,r,a,s,t])}function ro(t,e){let{relative:n}=e===void 0?{}:e,{future:r}=k.useContext(Nn),{matches:i}=k.useContext(Zn),{pathname:s}=kn(),a=JSON.stringify(gp(i,r.v7_relativeSplatPath));return k.useMemo(()=>xp(t,JSON.parse(a),s,n==="path"),[t,a,s,n])}function Ty(t,e){return Ry(t,e)}function Ry(t,e,n,r){es()||le(!1);let{navigator:i}=k.useContext(Nn),{matches:s}=k.useContext(Zn),a=s[s.length-1],o=a?a.params:{};a&&a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let u=kn(),d;if(e){var h;let b=typeof e=="string"?Fr(e):e;l==="/"||(h=b.pathname)!=null&&h.startsWith(l)||le(!1),d=b}else d=u;let f=d.pathname||"/",m=f;if(l!=="/"){let b=l.replace(/^\//,"").split("/");m="/"+f.replace(/^\//,"").split("/").slice(b.length).join("/")}let x=ly(t,{pathname:m}),y=Fy(x&&x.map(b=>Object.assign({},b,{params:Object.assign({},o,b.params),pathname:mn([l,i.encodeLocation?i.encodeLocation(b.pathname).pathname:b.pathname]),pathnameBase:b.pathnameBase==="/"?l:mn([l,i.encodeLocation?i.encodeLocation(b.pathnameBase).pathname:b.pathnameBase])})),s,n,r);return e&&y?k.createElement(no.Provider,{value:{location:Hi({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:nn.Pop}},y):y}function Dy(){let t=Hy(),e=Py(t)?t.status+" "+t.statusText:t instanceof Error?t.message:JSON.stringify(t),n=t instanceof Error?t.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return k.createElement(k.Fragment,null,k.createElement("h2",null,"Unexpected Application Error!"),k.createElement("h3",{style:{fontStyle:"italic"}},e),n?k.createElement("pre",{style:i},n):null,null)}const Oy=k.createElement(Dy,null);class Ly extends k.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return n.location!==e.location||n.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:n.error,location:n.location,revalidation:e.revalidation||n.revalidation}}componentDidCatch(e,n){console.error("React Router caught the following error during render",e,n)}render(){return this.state.error!==void 0?k.createElement(Zn.Provider,{value:this.props.routeContext},k.createElement(bp.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function zy(t){let{routeContext:e,match:n,children:r}=t,i=k.useContext(to);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),k.createElement(Zn.Provider,{value:e},r)}function Fy(t,e,n,r){var i;if(e===void 0&&(e=[]),n===void 0&&(n=null),r===void 0&&(r=null),t==null){var s;if(!n)return null;if(n.errors)t=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&e.length===0&&!n.initialized&&n.matches.length>0)t=n.matches;else return null}let a=t,o=(i=n)==null?void 0:i.errors;if(o!=null){let d=a.findIndex(h=>h.route.id&&(o==null?void 0:o[h.route.id])!==void 0);d>=0||le(!1),a=a.slice(0,Math.min(a.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<a.length;d++){let h=a[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(u=d),h.route.id){let{loaderData:f,errors:m}=n,x=h.route.loader&&f[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||x){l=!0,u>=0?a=a.slice(0,u+1):a=[a[0]];break}}}return a.reduceRight((d,h,f)=>{let m,x=!1,y=null,b=null;n&&(m=o&&h.route.id?o[h.route.id]:void 0,y=h.route.errorElement||Oy,l&&(u<0&&f===0?(Wy("route-fallback"),x=!0,b=null):u===f&&(x=!0,b=h.route.hydrateFallbackElement||null)));let g=e.concat(a.slice(0,f+1)),p=()=>{let v;return m?v=y:x?v=b:h.route.Component?v=k.createElement(h.route.Component,null):h.route.element?v=h.route.element:v=d,k.createElement(zy,{match:h,routeContext:{outlet:d,matches:g,isDataRoute:n!=null},children:v})};return n&&(h.route.ErrorBoundary||h.route.errorElement||f===0)?k.createElement(Ly,{location:n.location,revalidation:n.revalidation,component:y,error:m,children:p(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):p()},null)}var Sp=function(t){return t.UseBlocker="useBlocker",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t}(Sp||{}),_p=function(t){return t.UseBlocker="useBlocker",t.UseLoaderData="useLoaderData",t.UseActionData="useActionData",t.UseRouteError="useRouteError",t.UseNavigation="useNavigation",t.UseRouteLoaderData="useRouteLoaderData",t.UseMatches="useMatches",t.UseRevalidator="useRevalidator",t.UseNavigateStable="useNavigate",t.UseRouteId="useRouteId",t}(_p||{});function Ay(t){let e=k.useContext(to);return e||le(!1),e}function Iy(t){let e=k.useContext(vp);return e||le(!1),e}function Uy(t){let e=k.useContext(Zn);return e||le(!1),e}function Np(t){let e=Uy(),n=e.matches[e.matches.length-1];return n.route.id||le(!1),n.route.id}function Hy(){var t;let e=k.useContext(bp),n=Iy(),r=Np();return e!==void 0?e:(t=n.errors)==null?void 0:t[r]}function By(){let{router:t}=Ay(Sp.UseNavigateStable),e=Np(_p.UseNavigateStable),n=k.useRef(!1);return wp(()=>{n.current=!0}),k.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?t.navigate(i):t.navigate(i,Hi({fromRouteId:e},s)))},[t,e])}const Md={};function Wy(t,e,n){Md[t]||(Md[t]=!0)}function Vy(t,e){t==null||t.v7_startTransition,t==null||t.v7_relativeSplatPath}function Tn(t){le(!1)}function Yy(t){let{basename:e="/",children:n=null,location:r,navigationType:i=nn.Pop,navigator:s,static:a=!1,future:o}=t;es()&&le(!1);let l=e.replace(/^\/*/,"/"),u=k.useMemo(()=>({basename:l,navigator:s,static:a,future:Hi({v7_relativeSplatPath:!1},o)}),[l,o,s,a]);typeof r=="string"&&(r=Fr(r));let{pathname:d="/",search:h="",hash:f="",state:m=null,key:x="default"}=r,y=k.useMemo(()=>{let b=Tr(d,l);return b==null?null:{location:{pathname:b,search:h,hash:f,state:m,key:x},navigationType:i}},[l,d,h,f,m,x,i]);return y==null?null:k.createElement(Nn.Provider,{value:u},k.createElement(no.Provider,{children:n,value:y}))}function Xy(t){let{children:e,location:n}=t;return Ty(Vl(e),n)}new Promise(()=>{});function Vl(t,e){e===void 0&&(e=[]);let n=[];return k.Children.forEach(t,(r,i)=>{if(!k.isValidElement(r))return;let s=[...e,i];if(r.type===k.Fragment){n.push.apply(n,Vl(r.props.children,s));return}r.type!==Tn&&le(!1),!r.props.index||!r.props.children||le(!1);let a={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(a.children=Vl(r.props.children,s)),n.push(a)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _a(){return _a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},_a.apply(this,arguments)}function kp(t,e){if(t==null)return{};var n={},r=Object.keys(t),i,s;for(s=0;s<r.length;s++)i=r[s],!(e.indexOf(i)>=0)&&(n[i]=t[i]);return n}function Qy(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function Ky(t,e){return t.button===0&&(!e||e==="_self")&&!Qy(t)}function Yl(t){return t===void 0&&(t=""),new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((e,n)=>{let r=t[n];return e.concat(Array.isArray(r)?r.map(i=>[n,i]):[[n,r]])},[]))}function Gy(t,e){let n=Yl(t);return e&&e.forEach((r,i)=>{n.has(i)||e.getAll(i).forEach(s=>{n.append(i,s)})}),n}const qy=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Zy=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],Jy="6";try{window.__reactRouterVersion=Jy}catch{}const ev=k.createContext({isTransitioning:!1}),tv="startTransition",$d=Xg[tv];function nv(t){let{basename:e,children:n,future:r,window:i}=t,s=k.useRef();s.current==null&&(s.current=sy({window:i,v5Compat:!0}));let a=s.current,[o,l]=k.useState({action:a.action,location:a.location}),{v7_startTransition:u}=r||{},d=k.useCallback(h=>{u&&$d?$d(()=>l(h)):l(h)},[l,u]);return k.useLayoutEffect(()=>a.listen(d),[a,d]),k.useEffect(()=>Vy(r),[r]),k.createElement(Yy,{basename:e,children:n,location:o.location,navigationType:o.action,navigator:a,future:r})}const rv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",iv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,sv=k.forwardRef(function(e,n){let{onClick:r,relative:i,reloadDocument:s,replace:a,state:o,target:l,to:u,preventScrollReset:d,viewTransition:h}=e,f=kp(e,qy),{basename:m}=k.useContext(Nn),x,y=!1;if(typeof u=="string"&&iv.test(u)&&(x=u,rv))try{let v=new URL(window.location.href),w=u.startsWith("//")?new URL(v.protocol+u):new URL(u),_=Tr(w.pathname,m);w.origin===v.origin&&_!=null?u=_+w.search+w.hash:y=!0}catch{}let b=$y(u,{relative:i}),g=ov(u,{replace:a,state:o,target:l,preventScrollReset:d,relative:i,viewTransition:h});function p(v){r&&r(v),v.defaultPrevented||g(v)}return k.createElement("a",_a({},f,{href:x||b,onClick:y||s?r:p,ref:n,target:l}))}),ws=k.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:i=!1,className:s="",end:a=!1,style:o,to:l,viewTransition:u,children:d}=e,h=kp(e,Zy),f=ro(l,{relative:h.relative}),m=kn(),x=k.useContext(vp),{navigator:y,basename:b}=k.useContext(Nn),g=x!=null&&cv(f)&&u===!0,p=y.encodeLocation?y.encodeLocation(f).pathname:f.pathname,v=m.pathname,w=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;i||(v=v.toLowerCase(),w=w?w.toLowerCase():null,p=p.toLowerCase()),w&&b&&(w=Tr(w,b)||w);const _=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let S=v===p||!a&&v.startsWith(p)&&v.charAt(_)==="/",N=w!=null&&(w===p||!a&&w.startsWith(p)&&w.charAt(p.length)==="/"),j={isActive:S,isPending:N,isTransitioning:g},$=S?r:void 0,M;typeof s=="function"?M=s(j):M=[s,S?"active":null,N?"pending":null,g?"transitioning":null].filter(Boolean).join(" ");let E=typeof o=="function"?o(j):o;return k.createElement(sv,_a({},h,{"aria-current":$,className:M,ref:n,style:E,to:l,viewTransition:u}),typeof d=="function"?d(j):d)});var Xl;(function(t){t.UseScrollRestoration="useScrollRestoration",t.UseSubmit="useSubmit",t.UseSubmitFetcher="useSubmitFetcher",t.UseFetcher="useFetcher",t.useViewTransitionState="useViewTransitionState"})(Xl||(Xl={}));var Ed;(function(t){t.UseFetcher="useFetcher",t.UseFetchers="useFetchers",t.UseScrollRestoration="useScrollRestoration"})(Ed||(Ed={}));function av(t){let e=k.useContext(to);return e||le(!1),e}function ov(t,e){let{target:n,replace:r,state:i,preventScrollReset:s,relative:a,viewTransition:o}=e===void 0?{}:e,l=Ar(),u=kn(),d=ro(t,{relative:a});return k.useCallback(h=>{if(Ky(h,n)){h.preventDefault();let f=r!==void 0?r:Sa(u)===Sa(d);l(t,{replace:f,state:i,preventScrollReset:s,relative:a,viewTransition:o})}},[u,l,d,r,i,n,t,s,a,o])}function lv(t){let e=k.useRef(Yl(t)),n=k.useRef(!1),r=kn(),i=k.useMemo(()=>Gy(r.search,n.current?null:e.current),[r.search]),s=Ar(),a=k.useCallback((o,l)=>{const u=Yl(typeof o=="function"?o(i):o);n.current=!0,s("?"+u,l)},[s,i]);return[i,a]}function cv(t,e){e===void 0&&(e={});let n=k.useContext(ev);n==null&&le(!1);let{basename:r}=av(Xl.useViewTransitionState),i=ro(t,{relative:e.relative});if(!n.isTransitioning)return!1;let s=Tr(n.currentLocation.pathname,r)||n.currentLocation.pathname,a=Tr(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Wl(i.pathname,a)!=null||Wl(i.pathname,s)!=null}function Na(t){"@babel/helpers - typeof";return Na=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Na(t)}function Xn(t){if(t===null||t===!0||t===!1)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function ze(t,e){if(e.length<t)throw new TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function Mt(t){ze(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||Na(t)==="object"&&e==="[object Date]"?new Date(t.getTime()):typeof t=="number"||e==="[object Number]"?new Date(t):((typeof t=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function uv(t,e){ze(2,arguments);var n=Mt(t).getTime(),r=Xn(e);return new Date(n+r)}var dv={};function io(){return dv}function hv(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}function fv(t){return ze(1,arguments),t instanceof Date||Na(t)==="object"&&Object.prototype.toString.call(t)==="[object Date]"}function mv(t){if(ze(1,arguments),!fv(t)&&typeof t!="number")return!1;var e=Mt(t);return!isNaN(Number(e))}function pv(t,e){ze(2,arguments);var n=Xn(e);return uv(t,-n)}var gv=864e5;function xv(t){ze(1,arguments);var e=Mt(t),n=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var r=e.getTime(),i=n-r;return Math.floor(i/gv)+1}function ka(t){ze(1,arguments);var e=1,n=Mt(t),r=n.getUTCDay(),i=(r<e?7:0)+r-e;return n.setUTCDate(n.getUTCDate()-i),n.setUTCHours(0,0,0,0),n}function jp(t){ze(1,arguments);var e=Mt(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var i=ka(r),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var a=ka(s);return e.getTime()>=i.getTime()?n+1:e.getTime()>=a.getTime()?n:n-1}function yv(t){ze(1,arguments);var e=jp(t),n=new Date(0);n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0);var r=ka(n);return r}var vv=6048e5;function bv(t){ze(1,arguments);var e=Mt(t),n=ka(e).getTime()-yv(e).getTime();return Math.round(n/vv)+1}function ja(t,e){var n,r,i,s,a,o,l,u;ze(1,arguments);var d=io(),h=Xn((n=(r=(i=(s=e==null?void 0:e.weekStartsOn)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.weekStartsOn)!==null&&i!==void 0?i:d.weekStartsOn)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&n!==void 0?n:0);if(!(h>=0&&h<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var f=Mt(t),m=f.getUTCDay(),x=(m<h?7:0)+m-h;return f.setUTCDate(f.getUTCDate()-x),f.setUTCHours(0,0,0,0),f}function Cp(t,e){var n,r,i,s,a,o,l,u;ze(1,arguments);var d=Mt(t),h=d.getUTCFullYear(),f=io(),m=Xn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:f.firstWeekContainsDate)!==null&&r!==void 0?r:(l=f.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var x=new Date(0);x.setUTCFullYear(h+1,0,m),x.setUTCHours(0,0,0,0);var y=ja(x,e),b=new Date(0);b.setUTCFullYear(h,0,m),b.setUTCHours(0,0,0,0);var g=ja(b,e);return d.getTime()>=y.getTime()?h+1:d.getTime()>=g.getTime()?h:h-1}function wv(t,e){var n,r,i,s,a,o,l,u;ze(1,arguments);var d=io(),h=Xn((n=(r=(i=(s=e==null?void 0:e.firstWeekContainsDate)!==null&&s!==void 0?s:e==null||(a=e.locale)===null||a===void 0||(o=a.options)===null||o===void 0?void 0:o.firstWeekContainsDate)!==null&&i!==void 0?i:d.firstWeekContainsDate)!==null&&r!==void 0?r:(l=d.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&n!==void 0?n:1),f=Cp(t,e),m=new Date(0);m.setUTCFullYear(f,0,h),m.setUTCHours(0,0,0,0);var x=ja(m,e);return x}var Sv=6048e5;function _v(t,e){ze(1,arguments);var n=Mt(t),r=ja(n,e).getTime()-wv(n,e).getTime();return Math.round(r/Sv)+1}function Y(t,e){for(var n=t<0?"-":"",r=Math.abs(t).toString();r.length<e;)r="0"+r;return n+r}var Qt={y:function(e,n){var r=e.getUTCFullYear(),i=r>0?r:1-r;return Y(n==="yy"?i%100:i,n.length)},M:function(e,n){var r=e.getUTCMonth();return n==="M"?String(r+1):Y(r+1,2)},d:function(e,n){return Y(e.getUTCDate(),n.length)},a:function(e,n){var r=e.getUTCHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];case"aaaa":default:return r==="am"?"a.m.":"p.m."}},h:function(e,n){return Y(e.getUTCHours()%12||12,n.length)},H:function(e,n){return Y(e.getUTCHours(),n.length)},m:function(e,n){return Y(e.getUTCMinutes(),n.length)},s:function(e,n){return Y(e.getUTCSeconds(),n.length)},S:function(e,n){var r=n.length,i=e.getUTCMilliseconds(),s=Math.floor(i*Math.pow(10,r-3));return Y(s,n.length)}},er={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Nv={G:function(e,n,r){var i=e.getUTCFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return r.era(i,{width:"abbreviated"});case"GGGGG":return r.era(i,{width:"narrow"});case"GGGG":default:return r.era(i,{width:"wide"})}},y:function(e,n,r){if(n==="yo"){var i=e.getUTCFullYear(),s=i>0?i:1-i;return r.ordinalNumber(s,{unit:"year"})}return Qt.y(e,n)},Y:function(e,n,r,i){var s=Cp(e,i),a=s>0?s:1-s;if(n==="YY"){var o=a%100;return Y(o,2)}return n==="Yo"?r.ordinalNumber(a,{unit:"year"}):Y(a,n.length)},R:function(e,n){var r=jp(e);return Y(r,n.length)},u:function(e,n){var r=e.getUTCFullYear();return Y(r,n.length)},Q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"Q":return String(i);case"QQ":return Y(i,2);case"Qo":return r.ordinalNumber(i,{unit:"quarter"});case"QQQ":return r.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return r.quarter(i,{width:"wide",context:"formatting"})}},q:function(e,n,r){var i=Math.ceil((e.getUTCMonth()+1)/3);switch(n){case"q":return String(i);case"qq":return Y(i,2);case"qo":return r.ordinalNumber(i,{unit:"quarter"});case"qqq":return r.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return r.quarter(i,{width:"wide",context:"standalone"})}},M:function(e,n,r){var i=e.getUTCMonth();switch(n){case"M":case"MM":return Qt.M(e,n);case"Mo":return r.ordinalNumber(i+1,{unit:"month"});case"MMM":return r.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return r.month(i,{width:"wide",context:"formatting"})}},L:function(e,n,r){var i=e.getUTCMonth();switch(n){case"L":return String(i+1);case"LL":return Y(i+1,2);case"Lo":return r.ordinalNumber(i+1,{unit:"month"});case"LLL":return r.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return r.month(i,{width:"wide",context:"standalone"})}},w:function(e,n,r,i){var s=_v(e,i);return n==="wo"?r.ordinalNumber(s,{unit:"week"}):Y(s,n.length)},I:function(e,n,r){var i=bv(e);return n==="Io"?r.ordinalNumber(i,{unit:"week"}):Y(i,n.length)},d:function(e,n,r){return n==="do"?r.ordinalNumber(e.getUTCDate(),{unit:"date"}):Qt.d(e,n)},D:function(e,n,r){var i=xv(e);return n==="Do"?r.ordinalNumber(i,{unit:"dayOfYear"}):Y(i,n.length)},E:function(e,n,r){var i=e.getUTCDay();switch(n){case"E":case"EE":case"EEE":return r.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(i,{width:"short",context:"formatting"});case"EEEE":default:return r.day(i,{width:"wide",context:"formatting"})}},e:function(e,n,r,i){var s=e.getUTCDay(),a=(s-i.weekStartsOn+8)%7||7;switch(n){case"e":return String(a);case"ee":return Y(a,2);case"eo":return r.ordinalNumber(a,{unit:"day"});case"eee":return r.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(s,{width:"short",context:"formatting"});case"eeee":default:return r.day(s,{width:"wide",context:"formatting"})}},c:function(e,n,r,i){var s=e.getUTCDay(),a=(s-i.weekStartsOn+8)%7||7;switch(n){case"c":return String(a);case"cc":return Y(a,n.length);case"co":return r.ordinalNumber(a,{unit:"day"});case"ccc":return r.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(s,{width:"narrow",context:"standalone"});case"cccccc":return r.day(s,{width:"short",context:"standalone"});case"cccc":default:return r.day(s,{width:"wide",context:"standalone"})}},i:function(e,n,r){var i=e.getUTCDay(),s=i===0?7:i;switch(n){case"i":return String(s);case"ii":return Y(s,n.length);case"io":return r.ordinalNumber(s,{unit:"day"});case"iii":return r.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(i,{width:"short",context:"formatting"});case"iiii":default:return r.day(i,{width:"wide",context:"formatting"})}},a:function(e,n,r){var i=e.getUTCHours(),s=i/12>=1?"pm":"am";switch(n){case"a":case"aa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(e,n,r){var i=e.getUTCHours(),s;switch(i===12?s=er.noon:i===0?s=er.midnight:s=i/12>=1?"pm":"am",n){case"b":case"bb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(e,n,r){var i=e.getUTCHours(),s;switch(i>=17?s=er.evening:i>=12?s=er.afternoon:i>=4?s=er.morning:s=er.night,n){case"B":case"BB":case"BBB":return r.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return r.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(e,n,r){if(n==="ho"){var i=e.getUTCHours()%12;return i===0&&(i=12),r.ordinalNumber(i,{unit:"hour"})}return Qt.h(e,n)},H:function(e,n,r){return n==="Ho"?r.ordinalNumber(e.getUTCHours(),{unit:"hour"}):Qt.H(e,n)},K:function(e,n,r){var i=e.getUTCHours()%12;return n==="Ko"?r.ordinalNumber(i,{unit:"hour"}):Y(i,n.length)},k:function(e,n,r){var i=e.getUTCHours();return i===0&&(i=24),n==="ko"?r.ordinalNumber(i,{unit:"hour"}):Y(i,n.length)},m:function(e,n,r){return n==="mo"?r.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):Qt.m(e,n)},s:function(e,n,r){return n==="so"?r.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):Qt.s(e,n)},S:function(e,n){return Qt.S(e,n)},X:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();if(a===0)return"Z";switch(n){case"X":return Rd(a);case"XXXX":case"XX":return Rn(a);case"XXXXX":case"XXX":default:return Rn(a,":")}},x:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"x":return Rd(a);case"xxxx":case"xx":return Rn(a);case"xxxxx":case"xxx":default:return Rn(a,":")}},O:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+Td(a,":");case"OOOO":default:return"GMT"+Rn(a,":")}},z:function(e,n,r,i){var s=i._originalDate||e,a=s.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+Td(a,":");case"zzzz":default:return"GMT"+Rn(a,":")}},t:function(e,n,r,i){var s=i._originalDate||e,a=Math.floor(s.getTime()/1e3);return Y(a,n.length)},T:function(e,n,r,i){var s=i._originalDate||e,a=s.getTime();return Y(a,n.length)}};function Td(t,e){var n=t>0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=r%60;if(s===0)return n+String(i);var a=e;return n+String(i)+a+Y(s,2)}function Rd(t,e){if(t%60===0){var n=t>0?"-":"+";return n+Y(Math.abs(t)/60,2)}return Rn(t,e)}function Rn(t,e){var n=e||"",r=t>0?"-":"+",i=Math.abs(t),s=Y(Math.floor(i/60),2),a=Y(i%60,2);return r+s+n+a}var Dd=function(e,n){switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},Pp=function(e,n){switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},kv=function(e,n){var r=e.match(/(P+)(p+)?/)||[],i=r[1],s=r[2];if(!s)return Dd(e,n);var a;switch(i){case"P":a=n.dateTime({width:"short"});break;case"PP":a=n.dateTime({width:"medium"});break;case"PPP":a=n.dateTime({width:"long"});break;case"PPPP":default:a=n.dateTime({width:"full"});break}return a.replace("{{date}}",Dd(i,n)).replace("{{time}}",Pp(s,n))},jv={p:Pp,P:kv},Cv=["D","DD"],Pv=["YY","YYYY"];function Mv(t){return Cv.indexOf(t)!==-1}function $v(t){return Pv.indexOf(t)!==-1}function Od(t,e,n){if(t==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(t==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var Ev={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Tv=function(e,n,r){var i,s=Ev[e];return typeof s=="string"?i=s:n===1?i=s.one:i=s.other.replace("{{count}}",n.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+i:i+" ago":i};function zo(t){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth,r=t.formats[n]||t.formats[t.defaultWidth];return r}}var Rv={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Dv={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ov={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Lv={date:zo({formats:Rv,defaultWidth:"full"}),time:zo({formats:Dv,defaultWidth:"full"}),dateTime:zo({formats:Ov,defaultWidth:"full"})},zv={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Fv=function(e,n,r,i){return zv[e]};function Gr(t){return function(e,n){var r=n!=null&&n.context?String(n.context):"standalone",i;if(r==="formatting"&&t.formattingValues){var s=t.defaultFormattingWidth||t.defaultWidth,a=n!=null&&n.width?String(n.width):s;i=t.formattingValues[a]||t.formattingValues[s]}else{var o=t.defaultWidth,l=n!=null&&n.width?String(n.width):t.defaultWidth;i=t.values[l]||t.values[o]}var u=t.argumentCallback?t.argumentCallback(e):e;return i[u]}}var Av={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Iv={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},Uv={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Hv={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Bv={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Wv={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Vv=function(e,n){var r=Number(e),i=r%100;if(i>20||i<10)switch(i%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},Yv={ordinalNumber:Vv,era:Gr({values:Av,defaultWidth:"wide"}),quarter:Gr({values:Iv,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:Gr({values:Uv,defaultWidth:"wide"}),day:Gr({values:Hv,defaultWidth:"wide"}),dayPeriod:Gr({values:Bv,defaultWidth:"wide",formattingValues:Wv,defaultFormattingWidth:"wide"})};function qr(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],s=e.match(i);if(!s)return null;var a=s[0],o=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(o)?Qv(o,function(h){return h.test(a)}):Xv(o,function(h){return h.test(a)}),u;u=t.valueCallback?t.valueCallback(l):l,u=n.valueCallback?n.valueCallback(u):u;var d=e.slice(a.length);return{value:u,rest:d}}}function Xv(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}function Qv(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}function Kv(t){return function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var i=r[0],s=e.match(t.parsePattern);if(!s)return null;var a=t.valueCallback?t.valueCallback(s[0]):s[0];a=n.valueCallback?n.valueCallback(a):a;var o=e.slice(i.length);return{value:a,rest:o}}}var Gv=/^(\d+)(th|st|nd|rd)?/i,qv=/\d+/i,Zv={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Jv={any:[/^b/i,/^(a|c)/i]},e1={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},t1={any:[/1/i,/2/i,/3/i,/4/i]},n1={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},r1={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},i1={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},s1={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},a1={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},o1={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},l1={ordinalNumber:Kv({matchPattern:Gv,parsePattern:qv,valueCallback:function(e){return parseInt(e,10)}}),era:qr({matchPatterns:Zv,defaultMatchWidth:"wide",parsePatterns:Jv,defaultParseWidth:"any"}),quarter:qr({matchPatterns:e1,defaultMatchWidth:"wide",parsePatterns:t1,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:qr({matchPatterns:n1,defaultMatchWidth:"wide",parsePatterns:r1,defaultParseWidth:"any"}),day:qr({matchPatterns:i1,defaultMatchWidth:"wide",parsePatterns:s1,defaultParseWidth:"any"}),dayPeriod:qr({matchPatterns:a1,defaultMatchWidth:"any",parsePatterns:o1,defaultParseWidth:"any"})},c1={code:"en-US",formatDistance:Tv,formatLong:Lv,formatRelative:Fv,localize:Yv,match:l1,options:{weekStartsOn:0,firstWeekContainsDate:1}},u1=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,d1=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,h1=/^'([^]*?)'?$/,f1=/''/g,m1=/[a-zA-Z]/;function Ld(t,e,n){var r,i,s,a,o,l,u,d,h,f,m,x,y,b;ze(2,arguments);var g=String(e),p=io(),v=(r=(i=void 0)!==null&&i!==void 0?i:p.locale)!==null&&r!==void 0?r:c1,w=Xn((s=(a=(o=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&o!==void 0?o:p.firstWeekContainsDate)!==null&&a!==void 0?a:(u=p.locale)===null||u===void 0||(d=u.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&s!==void 0?s:1);if(!(w>=1&&w<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var _=Xn((h=(f=(m=(x=void 0)!==null&&x!==void 0?x:void 0)!==null&&m!==void 0?m:p.weekStartsOn)!==null&&f!==void 0?f:(y=p.locale)===null||y===void 0||(b=y.options)===null||b===void 0?void 0:b.weekStartsOn)!==null&&h!==void 0?h:0);if(!(_>=0&&_<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!v.localize)throw new RangeError("locale must contain localize property");if(!v.formatLong)throw new RangeError("locale must contain formatLong property");var S=Mt(t);if(!mv(S))throw new RangeError("Invalid time value");var N=hv(S),j=pv(S,N),$={firstWeekContainsDate:w,weekStartsOn:_,locale:v,_originalDate:S},M=g.match(d1).map(function(E){var L=E[0];if(L==="p"||L==="P"){var R=jv[L];return R(E,v.formatLong)}return E}).join("").match(u1).map(function(E){if(E==="''")return"'";var L=E[0];if(L==="'")return p1(E);var R=Nv[L];if(R)return $v(E)&&Od(E,e,String(t)),Mv(E)&&Od(E,e,String(t)),R(j,E,v.localize,$);if(L.match(m1))throw new RangeError("Format string contains an unescaped latin alphabet character `"+L+"`");return E}).join("");return M}function p1(t){var e=t.match(h1);return e?e[1].replace(f1,"'"):t}const Mp=k.createContext(),Ql={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function g1(t,e){switch(e.type){case"INITIALIZE_APP":return{...t,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:t.unitCost,timestamp:Ld(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,purchases:[n,...t.purchases],currentUnits:t.currentUnits+e.payload.units};case"UPDATE_USAGE":const r={id:Date.now(),date:new Date().toISOString(),previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usage:t.currentUnits-e.payload.currentUnits,timestamp:Ld(new Date,"yyyy-MM-dd HH:mm:ss")};return{...t,previousUnits:t.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[r,...t.usageHistory]};case"UPDATE_SETTINGS":return{...t,...e.payload};case"FACTORY_RESET":return{...Ql};case"DASHBOARD_RESET":return{...t,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...t,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...t,...e.payload};default:return t}}function x1({children:t}){const[e,n]=k.useReducer(g1,Ql);k.useEffect(()=>{const S="prepaid-meter-app-v1.1",N=localStorage.getItem(S);if(localStorage.removeItem("prepaid-meter-app"),N)try{const j=JSON.parse(N);if(!j.version||j.version!==Ql.version){console.log("Version mismatch detected, clearing old data"),localStorage.removeItem(S);return}n({type:"LOAD_STATE",payload:j})}catch(j){console.error("Error loading saved state:",j),localStorage.removeItem(S)}},[]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-app-v1.1",JSON.stringify(e))},[e]),k.useEffect(()=>{const S=new Date,N=S.getMonth(),j=S.getFullYear();if(e.lastMonthlyReset){const $=new Date(e.lastMonthlyReset),M=$.getMonth(),E=$.getFullYear();(j>E||j===E&&N>M)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:S.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]),k.useEffect(()=>{if(!e.notificationsEnabled||!e.isInitialized)return;const S=()=>{const j=new Date,[$,M]=e.notificationTime.split(":").map(Number),E=new Date;E.setHours($,M,0,0);const L=j.toDateString(),R=e.lastNotificationDate?new Date(e.lastNotificationDate).toDateString():null;j>=E&&R!==L&&("Notification"in window&&Notification.permission==="default"&&Notification.requestPermission(),"Notification"in window&&Notification.permission==="granted"&&(new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico"}),n({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:j.toISOString()}})))};S();const N=setInterval(S,6e4);return()=>clearInterval(N)},[e.notificationsEnabled,e.notificationTime,e.lastNotificationDate,e.isInitialized]);const r=e.previousUnits-e.currentUnits,i=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,s=e.purchases.reduce((S,N)=>S+N.currency,0),a=e.usageHistory.reduce((S,N)=>S+N.usage,0),o=new Date,l=new Date(o.getFullYear(),o.getMonth(),o.getDate()-o.getDay()),u=new Date(o.getFullYear(),o.getMonth(),1),d=e.purchases.filter(S=>new Date(S.date)>=l),h=e.purchases.filter(S=>new Date(S.date)>=u),f=e.usageHistory.filter(S=>new Date(S.date)>=l),m=e.usageHistory.filter(S=>new Date(S.date)>=u),x=d.reduce((S,N)=>S+N.currency,0),y=h.reduce((S,N)=>S+N.currency,0),b=f.reduce((S,N)=>S+N.usage,0),g=m.reduce((S,N)=>S+N.usage,0),_={state:e,dispatch:n,initializeApp:(S,N)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:S,unitCost:N}})},addPurchase:(S,N)=>{n({type:"ADD_PURCHASE",payload:{currency:S,units:N}})},updateUsage:S=>{n({type:"UPDATE_USAGE",payload:{currentUnits:S}})},updateSettings:S=>{n({type:"UPDATE_SETTINGS",payload:S})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:r,isThresholdExceeded:i,totalPurchases:s,totalUnitsUsed:a,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>{var N;return e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":((N=[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(j=>j.code===e.currency))==null?void 0:N.name)||"Unknown Currency"},weeklyPurchaseTotal:x,monthlyPurchaseTotal:y,weeklyUsageTotal:b,monthlyUsageTotal:g,weeklyPurchases:d,monthlyPurchases:h,weeklyUsage:f,monthlyUsage:m};return c.jsx(Mp.Provider,{value:_,children:t})}function Xe(){const t=k.useContext(Mp);if(!t)throw new Error("useApp must be used within an AppProvider");return t}const $p=k.createContext(),mt={electric:{name:"Electric Blue",primary:"bg-blue-600",secondary:"bg-blue-50",accent:"bg-blue-500",background:"bg-blue-25",text:"text-blue-900",textSecondary:"text-blue-700",border:"border-blue-200",card:"bg-blue-50",gradient:"from-blue-500 to-blue-700",light:"bg-blue-100",lighter:"bg-blue-50",dark:"bg-blue-700",darker:"bg-blue-800"},dark:{name:"Dark Mode",primary:"bg-gray-600",secondary:"bg-gray-700",accent:"bg-gray-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-600 to-gray-800",light:"bg-gray-700",lighter:"bg-gray-600",dark:"bg-gray-800",darker:"bg-gray-900"},green:{name:"Eco Green",primary:"bg-green-600",secondary:"bg-green-50",accent:"bg-green-500",background:"bg-green-25",text:"text-green-900",textSecondary:"text-green-700",border:"border-green-200",card:"bg-green-50",gradient:"from-green-500 to-green-700",light:"bg-green-100",lighter:"bg-green-50",dark:"bg-green-700",darker:"bg-green-800"},teal:{name:"Ocean Teal",primary:"bg-teal-600",secondary:"bg-teal-50",accent:"bg-teal-500",background:"bg-teal-25",text:"text-teal-900",textSecondary:"text-teal-700",border:"border-teal-200",card:"bg-teal-50",gradient:"from-teal-500 to-teal-700",light:"bg-teal-100",lighter:"bg-teal-50",dark:"bg-teal-700",darker:"bg-teal-800"},pink:{name:"Rose Pink",primary:"bg-pink-600",secondary:"bg-pink-50",accent:"bg-pink-500",background:"bg-pink-25",text:"text-pink-900",textSecondary:"text-pink-700",border:"border-pink-200",card:"bg-pink-50",gradient:"from-pink-500 to-pink-700",light:"bg-pink-100",lighter:"bg-pink-50",dark:"bg-pink-700",darker:"bg-pink-800"}};function y1({children:t}){const[e,n]=k.useState("electric");k.useEffect(()=>{const i=localStorage.getItem("prepaid-meter-theme");i&&mt[i]&&n(i)},[]),k.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]);const r={currentTheme:e,setCurrentTheme:n,theme:mt[e],themes:mt};return c.jsx($p.Provider,{value:r,children:c.jsx("div",{className:`${mt[e].background} ${mt[e].text} text-base min-h-screen transition-all duration-300`,style:{fontFamily:"Inter, system-ui, -apple-system, sans-serif",fontSize:"16px"},children:t})})}function me(){const t=k.useContext($p);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}var Ep={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},zd=gt.createContext&&gt.createContext(Ep),pn=function(){return pn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},pn.apply(this,arguments)},v1=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n};function Tp(t){return t&&t.map(function(e,n){return gt.createElement(e.tag,pn({key:n},e.attr),Tp(e.child))})}function J(t){return function(e){return gt.createElement(b1,pn({attr:pn({},t.attr)},e),Tp(t.child))}}function b1(t){var e=function(n){var r=t.attr,i=t.size,s=t.title,a=v1(t,["attr","size","title"]),o=i||n.size||"1em",l;return n.className&&(l=n.className),t.className&&(l=(l?l+" ":"")+t.className),gt.createElement("svg",pn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,a,{className:l,style:pn(pn({color:t.color||n.color},n.style),t.style),height:o,width:o,xmlns:"http://www.w3.org/2000/svg"}),s&&gt.createElement("title",null,s),t.children)};return zd!==void 0?gt.createElement(zd.Consumer,null,function(n){return e(n)}):e(Ep)}function w1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function S1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L4.707 9.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Rp(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 1a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm4-4a1 1 0 100 2h.01a1 1 0 100-2H13zM9 9a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zM7 8a1 1 0 000 2h.01a1 1 0 000-2H7z",clipRule:"evenodd"}}]})(t)}function Ca(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(t)}function Jc(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(t)}function _1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(t)}function Kl(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function Fd(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(t)}function Pa(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z",clipRule:"evenodd"}}]})(t)}function Ad(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(t)}function Bi(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(t)}function Dp(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z",clipRule:"evenodd"}}]})(t)}function Ye(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(t)}function wr(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function N1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(t)}function k1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z",clipRule:"evenodd"}}]})(t)}function Op(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"}}]})(t)}function Re(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(t)}function j1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(t)}function C1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(t)}function Lp(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(t)}function P1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"}}]})(t)}function M1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(t)}function $1(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(t)}function pt(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(t)}function zp(t){return J({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(t)}function E1({size:t="md",animated:e=!0,showText:n=!0}){const{theme:r}=me(),[i,s]=k.useState(!1),a={sm:{logo:"h-12 w-12",text:"text-sm",icon:"h-6 w-6"},md:{logo:"h-16 w-16",text:"text-lg",icon:"h-8 w-8"},lg:{logo:"h-20 w-20",text:"text-xl",icon:"h-10 w-10"},xl:{logo:"h-24 w-24",text:"text-2xl",icon:"h-12 w-12"}},o=a[t]||a.md;return c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("div",{className:`${o.logo} flex items-center justify-center`,children:i?c.jsx("div",{className:`${o.logo} rounded-2xl bg-gradient-to-br ${r.gradient} shadow-lg flex items-center justify-center ${e?"animate-pulse":""}`,children:c.jsx(Re,{className:`${o.icon} text-white`})}):c.jsx("img",{src:"/oie_transparent (1).png",alt:"Prepaid User Electricity Logo",className:`${o.logo} object-contain`,onError:()=>s(!0),onLoad:()=>s(!1)})}),n&&c.jsxs("div",{className:"flex flex-col",children:[c.jsx("h1",{className:`${o.text} font-black ${r.text} leading-tight`,children:"Prepaid User"}),c.jsx("p",{className:`text-base font-bold ${r.textSecondary} tracking-wider leading-tight`,children:"Electricity"})]})]})}function T1({onMenuClick:t}){const{theme:e,currentTheme:n}=me(),{state:r}=Xe(),i=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-500/90 backdrop-blur-xl border-white/30",green:"bg-green-500/90 backdrop-blur-xl border-white/30",teal:"bg-teal-500/90 backdrop-blur-xl border-white/30",pink:"bg-pink-500/90 backdrop-blur-xl border-white/30"}[n]||"bg-blue-500/90 backdrop-blur-xl border-white/30",s=()=>n==="dark"?e.text:"text-white drop-shadow-sm";return c.jsxs("header",{className:`${i()} border-b px-4 py-3 flex items-center justify-between shadow-xl`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsx("button",{onClick:t,className:"lg:hidden p-2 rounded-xl bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg",children:c.jsx(j1,{className:"h-6 w-6"})}),c.jsx("div",{className:"flex items-center",children:c.jsx(E1,{size:"sm",animated:!0,showText:!1})})]}),c.jsxs("div",{className:"flex items-center space-x-4 bg-black/30 backdrop-blur-md rounded-xl px-4 py-2 border border-white/30 shadow-lg",children:[c.jsxs("div",{className:"text-right",children:[c.jsx("p",{className:`text-sm ${s()}`,children:"Current Units"}),c.jsx("p",{className:`text-lg font-bold ${s()}`,children:r.currentUnits.toFixed(2)})]}),c.jsx("div",{className:"w-3 h-3 rounded-full bg-white/80 pulse-glow shadow-sm"})]})]})}const Id=[{name:"Dashboard",href:"/dashboard",icon:Op},{name:"Purchases",href:"/purchases",icon:P1},{name:"Usage",href:"/usage",icon:Jc},{name:"History",href:"/history",icon:Pa}],Ud=[{name:"General Settings",href:"/settings?section=general",icon:Bi},{name:"Appearance",href:"/settings?section=appearance",icon:Dp},{name:"Reset Options",href:"/settings?section=reset",icon:Lp}];function R1({isOpen:t,onClose:e}){const{theme:n,currentTheme:r}=me(),i=kn(),[s,a]=k.useState(!1),o=()=>r==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-500/90 backdrop-blur-xl border-white/30",green:"bg-green-500/90 backdrop-blur-xl border-white/30",teal:"bg-teal-500/90 backdrop-blur-xl border-white/30",pink:"bg-pink-500/90 backdrop-blur-xl border-white/30"}[r]||"bg-blue-500/90 backdrop-blur-xl border-white/30",l=()=>r==="dark"?n.text:"text-white drop-shadow-sm",u=()=>r==="dark"?"bg-white/15 backdrop-blur-sm":"bg-white/25 backdrop-blur-sm",d=()=>r==="dark"?"bg-white/20 backdrop-blur-md":"bg-white/35 backdrop-blur-md";return c.jsxs(c.Fragment,{children:[c.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${o()} border-r shadow-2xl`,children:c.jsxs("div",{className:"flex flex-col w-full",children:[c.jsx("div",{className:"flex items-center justify-center h-16 px-4 border-b border-white/15",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[Id.map(h=>c.jsxs(ws,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("button",{onClick:()=>a(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Bi,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(Kl,{className:"h-4 w-4"}):c.jsx(Fd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-1 mt-2",children:Ud.map(h=>c.jsxs(ws,{to:h.href,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})}),c.jsx("div",{className:`lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${o()} transform transition-transform duration-300 ease-in-out shadow-2xl ${t?"translate-x-0":"-translate-x-full"}`,children:c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:"flex items-center justify-between h-16 px-4 border-b border-white/15",children:[c.jsx("div",{className:"flex items-center",children:c.jsx("h2",{className:`text-xl font-bold ${l()} tracking-wider drop-shadow-sm`,children:"MENU"})}),c.jsx("button",{onClick:e,className:`p-2 rounded-xl ${l()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`,children:c.jsx(zp,{className:"h-6 w-6"})})]}),c.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[Id.map(h=>c.jsxs(ws,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${f?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsx(h.icon,{className:"mr-3 h-5 w-5"}),h.name]},h.name)),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("button",{onClick:()=>a(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${i.pathname.includes("/settings")?`${d()} ${l()} shadow-lg border border-white/30`:`${l()} hover:${u()} hover:shadow-md hover:border hover:border-white/20`}`,children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx(Bi,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?c.jsx(Kl,{className:"h-4 w-4"}):c.jsx(Fd,{className:"h-4 w-4"})]}),s&&c.jsx("div",{className:"ml-4 space-y-1 mt-2",children:Ud.map(h=>c.jsxs(ws,{to:h.href,onClick:e,className:({isActive:f})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${f?`bg-white/30 backdrop-blur-sm ${l()} shadow-md border border-white/25`:`${l()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[c.jsx(h.icon,{className:"mr-3 h-4 w-4"}),h.name]},h.name))})]})]})]})})]})}function D1(){const t=kn(),e=Ar(),{theme:n}=me(),r=[{id:"dashboard",path:"/",icon:Op,label:"Dashboard"},{id:"purchases",path:"/purchases",icon:Ye,label:"Purchases"},{id:"usage",path:"/usage",icon:pt,label:"Usage"},{id:"history",path:"/history",icon:Pa,label:"History"}],i=s=>s==="/"?t.pathname==="/"||t.pathname==="/dashboard":t.pathname===s;return c.jsx("div",{className:`md:hidden fixed bottom-0 left-0 right-0 z-30 ${n.card} border-t ${n.border} backdrop-blur-lg`,children:c.jsx("div",{className:"flex items-center justify-around px-2 py-2",children:r.map(s=>{const a=s.icon,o=i(s.path);return c.jsxs("button",{onClick:()=>e(s.path),className:`flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-200 min-w-0 flex-1 ${o?`${n.primary} text-white shadow-lg transform scale-105`:`${n.text} hover:${n.secondary}`}`,children:[c.jsx(a,{className:`h-5 w-5 ${o?"text-white":n.textSecondary}`}),c.jsx("span",{className:`text-xs font-medium mt-1 truncate ${o?"text-white":n.textSecondary}`,children:s.label})]},s.id)})})})}/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function ts(t){return t+.5|0}const rn=(t,e,n)=>Math.max(Math.min(t,n),e);function oi(t){return rn(ts(t*2.55),0,255)}function gn(t){return rn(ts(t*255),0,255)}function Lt(t){return rn(ts(t/2.55)/100,0,1)}function Hd(t){return rn(ts(t*100),0,100)}const et={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Gl=[..."0123456789ABCDEF"],O1=t=>Gl[t&15],L1=t=>Gl[(t&240)>>4]+Gl[t&15],Ss=t=>(t&240)>>4===(t&15),z1=t=>Ss(t.r)&&Ss(t.g)&&Ss(t.b)&&Ss(t.a);function F1(t){var e=t.length,n;return t[0]==="#"&&(e===4||e===5?n={r:255&et[t[1]]*17,g:255&et[t[2]]*17,b:255&et[t[3]]*17,a:e===5?et[t[4]]*17:255}:(e===7||e===9)&&(n={r:et[t[1]]<<4|et[t[2]],g:et[t[3]]<<4|et[t[4]],b:et[t[5]]<<4|et[t[6]],a:e===9?et[t[7]]<<4|et[t[8]]:255})),n}const A1=(t,e)=>t<255?e(t):"";function I1(t){var e=z1(t)?O1:L1;return t?"#"+e(t.r)+e(t.g)+e(t.b)+A1(t.a,e):void 0}const U1=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Fp(t,e,n){const r=e*Math.min(n,1-n),i=(s,a=(s+t/30)%12)=>n-r*Math.max(Math.min(a-3,9-a,1),-1);return[i(0),i(8),i(4)]}function H1(t,e,n){const r=(i,s=(i+t/60)%6)=>n-n*e*Math.max(Math.min(s,4-s,1),0);return[r(5),r(3),r(1)]}function B1(t,e,n){const r=Fp(t,1,.5);let i;for(e+n>1&&(i=1/(e+n),e*=i,n*=i),i=0;i<3;i++)r[i]*=1-e-n,r[i]+=e;return r}function W1(t,e,n,r,i){return t===i?(e-n)/r+(e<n?6:0):e===i?(n-t)/r+2:(t-e)/r+4}function eu(t){const n=t.r/255,r=t.g/255,i=t.b/255,s=Math.max(n,r,i),a=Math.min(n,r,i),o=(s+a)/2;let l,u,d;return s!==a&&(d=s-a,u=o>.5?d/(2-s-a):d/(s+a),l=W1(n,r,i,d,s),l=l*60+.5),[l|0,u||0,o]}function tu(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(gn)}function nu(t,e,n){return tu(Fp,t,e,n)}function V1(t,e,n){return tu(B1,t,e,n)}function Y1(t,e,n){return tu(H1,t,e,n)}function Ap(t){return(t%360+360)%360}function X1(t){const e=U1.exec(t);let n=255,r;if(!e)return;e[5]!==r&&(n=e[6]?oi(+e[5]):gn(+e[5]));const i=Ap(+e[2]),s=+e[3]/100,a=+e[4]/100;return e[1]==="hwb"?r=V1(i,s,a):e[1]==="hsv"?r=Y1(i,s,a):r=nu(i,s,a),{r:r[0],g:r[1],b:r[2],a:n}}function Q1(t,e){var n=eu(t);n[0]=Ap(n[0]+e),n=nu(n),t.r=n[0],t.g=n[1],t.b=n[2]}function K1(t){if(!t)return;const e=eu(t),n=e[0],r=Hd(e[1]),i=Hd(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${i}%, ${Lt(t.a)})`:`hsl(${n}, ${r}%, ${i}%)`}const Bd={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Wd={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function G1(){const t={},e=Object.keys(Wd),n=Object.keys(Bd);let r,i,s,a,o;for(r=0;r<e.length;r++){for(a=o=e[r],i=0;i<n.length;i++)s=n[i],o=o.replace(s,Bd[s]);s=parseInt(Wd[a],16),t[o]=[s>>16&255,s>>8&255,s&255]}return t}let _s;function q1(t){_s||(_s=G1(),_s.transparent=[0,0,0,0]);const e=_s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const Z1=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function J1(t){const e=Z1.exec(t);let n=255,r,i,s;if(e){if(e[7]!==r){const a=+e[7];n=e[8]?oi(a):rn(a*255,0,255)}return r=+e[1],i=+e[3],s=+e[5],r=255&(e[2]?oi(r):rn(r,0,255)),i=255&(e[4]?oi(i):rn(i,0,255)),s=255&(e[6]?oi(s):rn(s,0,255)),{r,g:i,b:s,a:n}}}function eb(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Lt(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Fo=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,tr=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function tb(t,e,n){const r=tr(Lt(t.r)),i=tr(Lt(t.g)),s=tr(Lt(t.b));return{r:gn(Fo(r+n*(tr(Lt(e.r))-r))),g:gn(Fo(i+n*(tr(Lt(e.g))-i))),b:gn(Fo(s+n*(tr(Lt(e.b))-s))),a:t.a+n*(e.a-t.a)}}function Ns(t,e,n){if(t){let r=eu(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,e===0?360:1)),r=nu(r),t.r=r[0],t.g=r[1],t.b=r[2]}}function Ip(t,e){return t&&Object.assign(e||{},t)}function Vd(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=gn(t[3]))):(e=Ip(t,{r:0,g:0,b:0,a:1}),e.a=gn(e.a)),e}function nb(t){return t.charAt(0)==="r"?J1(t):X1(t)}class Wi{constructor(e){if(e instanceof Wi)return e;const n=typeof e;let r;n==="object"?r=Vd(e):n==="string"&&(r=F1(e)||q1(e)||nb(e)),this._rgb=r,this._valid=!!r}get valid(){return this._valid}get rgb(){var e=Ip(this._rgb);return e&&(e.a=Lt(e.a)),e}set rgb(e){this._rgb=Vd(e)}rgbString(){return this._valid?eb(this._rgb):void 0}hexString(){return this._valid?I1(this._rgb):void 0}hslString(){return this._valid?K1(this._rgb):void 0}mix(e,n){if(e){const r=this.rgb,i=e.rgb;let s;const a=n===s?.5:n,o=2*a-1,l=r.a-i.a,u=((o*l===-1?o:(o+l)/(1+o*l))+1)/2;s=1-u,r.r=255&u*r.r+s*i.r+.5,r.g=255&u*r.g+s*i.g+.5,r.b=255&u*r.b+s*i.b+.5,r.a=a*r.a+(1-a)*i.a,this.rgb=r}return this}interpolate(e,n){return e&&(this._rgb=tb(this._rgb,e._rgb,n)),this}clone(){return new Wi(this.rgb)}alpha(e){return this._rgb.a=gn(e),this}clearer(e){const n=this._rgb;return n.a*=1-e,this}greyscale(){const e=this._rgb,n=ts(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=n,this}opaquer(e){const n=this._rgb;return n.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Ns(this._rgb,2,e),this}darken(e){return Ns(this._rgb,2,-e),this}saturate(e){return Ns(this._rgb,1,e),this}desaturate(e){return Ns(this._rgb,1,-e),this}rotate(e){return Q1(this._rgb,e),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Et(){}const rb=(()=>{let t=0;return()=>t++})();function q(t){return t==null}function ge(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function B(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function lt(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function St(t,e){return lt(t)?t:e}function W(t,e){return typeof t>"u"?e:t}const ib=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100:+t/e,Up=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function te(t,e,n){if(t&&typeof t.call=="function")return t.apply(n,e)}function X(t,e,n,r){let i,s,a;if(ge(t))for(s=t.length,i=0;i<s;i++)e.call(n,t[i],i);else if(B(t))for(a=Object.keys(t),s=a.length,i=0;i<s;i++)e.call(n,t[a[i]],a[i])}function Ma(t,e){let n,r,i,s;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(i=t[n],s=e[n],i.datasetIndex!==s.datasetIndex||i.index!==s.index)return!1;return!0}function $a(t){if(ge(t))return t.map($a);if(B(t)){const e=Object.create(null),n=Object.keys(t),r=n.length;let i=0;for(;i<r;++i)e[n[i]]=$a(t[n[i]]);return e}return t}function Hp(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function sb(t,e,n,r){if(!Hp(t))return;const i=e[t],s=n[t];B(i)&&B(s)?Vi(i,s,r):e[t]=$a(s)}function Vi(t,e,n){const r=ge(e)?e:[e],i=r.length;if(!B(t))return t;n=n||{};const s=n.merger||sb;let a;for(let o=0;o<i;++o){if(a=r[o],!B(a))continue;const l=Object.keys(a);for(let u=0,d=l.length;u<d;++u)s(l[u],t,a,n)}return t}function wi(t,e){return Vi(t,e,{merger:ab})}function ab(t,e,n){if(!Hp(t))return;const r=e[t],i=n[t];B(r)&&B(i)?wi(r,i):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=$a(i))}const Yd={"":t=>t,x:t=>t.x,y:t=>t.y};function ob(t){const e=t.split("."),n=[];let r="";for(const i of e)r+=i,r.endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}function lb(t){const e=ob(t);return n=>{for(const r of e){if(r==="")break;n=n&&n[r]}return n}}function Qn(t,e){return(Yd[e]||(Yd[e]=lb(e)))(t)}function ru(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Yi=t=>typeof t<"u",bn=t=>typeof t=="function",Xd=(t,e)=>{if(t.size!==e.size)return!1;for(const n of t)if(!e.has(n))return!1;return!0};function cb(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const fe=Math.PI,he=2*fe,Ea=Number.POSITIVE_INFINITY,ub=fe/180,ye=fe/2,jn=fe/4,Qd=fe*2/3,Bp=Math.log10,xn=Math.sign;function Xs(t,e,n){return Math.abs(t-e)<n}function Kd(t){const e=Math.round(t);t=Xs(t,e,t/1e3)?e:t;const n=Math.pow(10,Math.floor(Bp(t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function db(t){const e=[],n=Math.sqrt(t);let r;for(r=1;r<n;r++)t%r===0&&(e.push(r),e.push(t/r));return n===(n|0)&&e.push(n),e.sort((i,s)=>i-s).pop(),e}function hb(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Ta(t){return!hb(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function fb(t,e){const n=Math.round(t);return n-e<=t&&n+e>=t}function mb(t,e,n){let r,i,s;for(r=0,i=t.length;r<i;r++)s=t[r][n],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function At(t){return t*(fe/180)}function pb(t){return t*(180/fe)}function Gd(t){if(!lt(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Wp(t,e){const n=e.x-t.x,r=e.y-t.y,i=Math.sqrt(n*n+r*r);let s=Math.atan2(r,n);return s<-.5*fe&&(s+=he),{angle:s,distance:i}}function gb(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Cn(t){return(t%he+he)%he}function Ra(t,e,n,r){const i=Cn(t),s=Cn(e),a=Cn(n),o=Cn(s-i),l=Cn(a-i),u=Cn(i-s),d=Cn(i-a);return i===s||i===a||r&&s===a||o>l&&u<d}function He(t,e,n){return Math.max(e,Math.min(n,t))}function xb(t){return He(t,-32768,32767)}function Fn(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function iu(t,e,n){n=n||(a=>t[a]<e);let r=t.length-1,i=0,s;for(;r-i>1;)s=i+r>>1,n(s)?i=s:r=s;return{lo:i,hi:r}}const ql=(t,e,n,r)=>iu(t,n,r?i=>{const s=t[i][e];return s<n||s===n&&t[i+1][e]===n}:i=>t[i][e]<n),yb=(t,e,n)=>iu(t,n,r=>t[r][e]>=n);function vb(t,e,n){let r=0,i=t.length;for(;r<i&&t[r]<e;)r++;for(;i>r&&t[i-1]>n;)i--;return r>0||i<t.length?t.slice(r,i):t}const Vp=["push","pop","shift","splice","unshift"];function bb(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),Vp.forEach(n=>{const r="_onData"+ru(n),i=t[n];Object.defineProperty(t,n,{configurable:!0,enumerable:!1,value(...s){const a=i.apply(this,s);return t._chartjs.listeners.forEach(o=>{typeof o[r]=="function"&&o[r](...s)}),a}})})}function qd(t,e){const n=t._chartjs;if(!n)return;const r=n.listeners,i=r.indexOf(e);i!==-1&&r.splice(i,1),!(r.length>0)&&(Vp.forEach(s=>{delete t[s]}),delete t._chartjs)}function Yp(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Xp=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function Qp(t,e){let n=[],r=!1;return function(...i){n=i,r||(r=!0,Xp.call(window,()=>{r=!1,t.apply(e,n)}))}}function wb(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}const su=t=>t==="start"?"left":t==="end"?"right":"center",je=(t,e,n)=>t==="start"?e:t==="end"?n:(e+n)/2,Sb=(t,e,n,r)=>t===(r?"left":"right")?n:t==="center"?(e+n)/2:e,ks=t=>t===0||t===1,Zd=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*he/n)),Jd=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*he/n)+1,Si={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ye)+1,easeOutSine:t=>Math.sin(t*ye),easeInOutSine:t=>-.5*(Math.cos(fe*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>ks(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>ks(t)?t:Zd(t,.075,.3),easeOutElastic:t=>ks(t)?t:Jd(t,.075,.3),easeInOutElastic(t){return ks(t)?t:t<.5?.5*Zd(t*2,.1125,.45):.5+.5*Jd(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Si.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Si.easeInBounce(t*2)*.5:Si.easeOutBounce(t*2-1)*.5+.5};function Kp(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function eh(t){return Kp(t)?t:new Wi(t)}function Ao(t){return Kp(t)?t:new Wi(t).saturate(.5).darken(.1).hexString()}const _b=["x","y","borderWidth","radius","tension"],Nb=["color","borderColor","backgroundColor"];function kb(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:Nb},numbers:{type:"number",properties:_b}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function jb(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const th=new Map;function Cb(t,e){e=e||{};const n=t+JSON.stringify(e);let r=th.get(n);return r||(r=new Intl.NumberFormat(t,e),th.set(n,r)),r}function au(t,e,n){return Cb(e,n).format(t)}const Pb={values(t){return ge(t)?t:""+t},numeric(t,e,n){if(t===0)return"0";const r=this.chart.options.locale;let i,s=t;if(n.length>1){const u=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(u<1e-4||u>1e15)&&(i="scientific"),s=Mb(t,n)}const a=Bp(Math.abs(s)),o=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:i,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),au(t,r,l)}};function Mb(t,e){let n=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(n)>=1&&t!==Math.floor(t)&&(n=t-Math.floor(t)),n}var Gp={formatters:Pb};function $b(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,n)=>n.lineWidth,tickColor:(e,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Gp.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Kn=Object.create(null),Zl=Object.create(null);function _i(t,e){if(!e)return t;const n=e.split(".");for(let r=0,i=n.length;r<i;++r){const s=n[r];t=t[s]||(t[s]=Object.create(null))}return t}function Io(t,e,n){return typeof e=="string"?Vi(_i(t,e),n):Vi(_i(t,""),e)}class Eb{constructor(e,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=r=>r.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(r,i)=>Ao(i.backgroundColor),this.hoverBorderColor=(r,i)=>Ao(i.borderColor),this.hoverColor=(r,i)=>Ao(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(n)}set(e,n){return Io(this,e,n)}get(e){return _i(this,e)}describe(e,n){return Io(Zl,e,n)}override(e,n){return Io(Kn,e,n)}route(e,n,r,i){const s=_i(this,e),a=_i(this,r),o="_"+n;Object.defineProperties(s,{[o]:{value:s[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[o],u=a[i];return B(l)?Object.assign({},u,l):W(l,u)},set(l){this[o]=l}}})}apply(e){e.forEach(n=>n(this))}}var ue=new Eb({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[kb,jb,$b]);function Tb(t){return!t||q(t.size)||q(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function nh(t,e,n,r,i){let s=e[i];return s||(s=e[i]=t.measureText(i).width,n.push(i)),s>r&&(r=s),r}function Pn(t,e,n){const r=t.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((e-i)*r)/r+i}function rh(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ih(t,e,n,r){qp(t,e,n,r,null)}function qp(t,e,n,r,i){let s,a,o,l,u,d,h,f;const m=e.pointStyle,x=e.rotation,y=e.radius;let b=(x||0)*ub;if(m&&typeof m=="object"&&(s=m.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(n,r),t.rotate(b),t.drawImage(m,-m.width/2,-m.height/2,m.width,m.height),t.restore();return}if(!(isNaN(y)||y<=0)){switch(t.beginPath(),m){default:i?t.ellipse(n,r,i/2,y,0,0,he):t.arc(n,r,y,0,he),t.closePath();break;case"triangle":d=i?i/2:y,t.moveTo(n+Math.sin(b)*d,r-Math.cos(b)*y),b+=Qd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*y),b+=Qd,t.lineTo(n+Math.sin(b)*d,r-Math.cos(b)*y),t.closePath();break;case"rectRounded":u=y*.516,l=y-u,a=Math.cos(b+jn)*l,h=Math.cos(b+jn)*(i?i/2-u:l),o=Math.sin(b+jn)*l,f=Math.sin(b+jn)*(i?i/2-u:l),t.arc(n-h,r-o,u,b-fe,b-ye),t.arc(n+f,r-a,u,b-ye,b),t.arc(n+h,r+o,u,b,b+ye),t.arc(n-f,r+a,u,b+ye,b+fe),t.closePath();break;case"rect":if(!x){l=Math.SQRT1_2*y,d=i?i/2:l,t.rect(n-d,r-l,2*d,2*l);break}b+=jn;case"rectRot":h=Math.cos(b)*(i?i/2:y),a=Math.cos(b)*y,o=Math.sin(b)*y,f=Math.sin(b)*(i?i/2:y),t.moveTo(n-h,r-o),t.lineTo(n+f,r-a),t.lineTo(n+h,r+o),t.lineTo(n-f,r+a),t.closePath();break;case"crossRot":b+=jn;case"cross":h=Math.cos(b)*(i?i/2:y),a=Math.cos(b)*y,o=Math.sin(b)*y,f=Math.sin(b)*(i?i/2:y),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a);break;case"star":h=Math.cos(b)*(i?i/2:y),a=Math.cos(b)*y,o=Math.sin(b)*y,f=Math.sin(b)*(i?i/2:y),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a),b+=jn,h=Math.cos(b)*(i?i/2:y),a=Math.cos(b)*y,o=Math.sin(b)*y,f=Math.sin(b)*(i?i/2:y),t.moveTo(n-h,r-o),t.lineTo(n+h,r+o),t.moveTo(n+f,r-a),t.lineTo(n-f,r+a);break;case"line":a=i?i/2:Math.cos(b)*y,o=Math.sin(b)*y,t.moveTo(n-a,r-o),t.lineTo(n+a,r+o);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(b)*(i?i/2:y),r+Math.sin(b)*y);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function Zp(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function ou(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function lu(t){t.restore()}function Rb(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),q(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function Db(t,e,n,r,i){if(i.strikethrough||i.underline){const s=t.measureText(r),a=e-s.actualBoundingBoxLeft,o=e+s.actualBoundingBoxRight,l=n-s.actualBoundingBoxAscent,u=n+s.actualBoundingBoxDescent,d=i.strikethrough?(l+u)/2:u;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=i.decorationWidth||2,t.moveTo(a,d),t.lineTo(o,d),t.stroke()}}function Ob(t,e){const n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}function Xi(t,e,n,r,i,s={}){const a=ge(e)?e:[e],o=s.strokeWidth>0&&s.strokeColor!=="";let l,u;for(t.save(),t.font=i.string,Rb(t,s),l=0;l<a.length;++l)u=a[l],s.backdrop&&Ob(t,s.backdrop),o&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),q(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(u,n,r,s.maxWidth)),t.fillText(u,n,r,s.maxWidth),Db(t,n,r,u,s),r+=Number(i.lineHeight);t.restore()}function Da(t,e){const{x:n,y:r,w:i,h:s,radius:a}=e;t.arc(n+a.topLeft,r+a.topLeft,a.topLeft,1.5*fe,fe,!0),t.lineTo(n,r+s-a.bottomLeft),t.arc(n+a.bottomLeft,r+s-a.bottomLeft,a.bottomLeft,fe,ye,!0),t.lineTo(n+i-a.bottomRight,r+s),t.arc(n+i-a.bottomRight,r+s-a.bottomRight,a.bottomRight,ye,0,!0),t.lineTo(n+i,r+a.topRight),t.arc(n+i-a.topRight,r+a.topRight,a.topRight,0,-ye,!0),t.lineTo(n+a.topLeft,r)}const Lb=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,zb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Fb(t,e){const n=(""+t).match(Lb);if(!n||n[1]==="normal")return e*1.2;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100;break}return e*t}const Ab=t=>+t||0;function cu(t,e){const n={},r=B(e),i=r?Object.keys(e):e,s=B(t)?r?a=>W(t[a],t[e[a]]):a=>t[a]:()=>t;for(const a of i)n[a]=Ab(s(a));return n}function Jp(t){return cu(t,{top:"y",right:"x",bottom:"y",left:"x"})}function Sr(t){return cu(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ct(t){const e=Jp(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function Pe(t,e){t=t||{},e=e||ue.font;let n=W(t.size,e.size);typeof n=="string"&&(n=parseInt(n,10));let r=W(t.style,e.style);r&&!(""+r).match(zb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);const i={family:W(t.family,e.family),lineHeight:Fb(W(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:W(t.weight,e.weight),string:""};return i.string=Tb(i),i}function js(t,e,n,r){let i,s,a;for(i=0,s=t.length;i<s;++i)if(a=t[i],a!==void 0&&a!==void 0)return a}function Ib(t,e,n){const{min:r,max:i}=t,s=Up(e,(i-r)/2),a=(o,l)=>n&&o===0?0:o+l;return{min:a(r,-Math.abs(s)),max:a(i,s)}}function Ir(t,e){return Object.assign(Object.create(t),e)}function uu(t,e=[""],n,r,i=()=>t[0]){const s=n||t;typeof r>"u"&&(r=rg("_fallback",t));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:r,_getTarget:i,override:o=>uu([o,...t],e,s,r)};return new Proxy(a,{deleteProperty(o,l){return delete o[l],delete o._keys,delete t[0][l],!0},get(o,l){return tg(o,l,()=>Qb(l,e,t,o))},getOwnPropertyDescriptor(o,l){return Reflect.getOwnPropertyDescriptor(o._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(o,l){return ah(o).includes(l)},ownKeys(o){return ah(o)},set(o,l,u){const d=o._storage||(o._storage=i());return o[l]=d[l]=u,delete o._keys,!0}})}function Rr(t,e,n,r){const i={_cacheable:!1,_proxy:t,_context:e,_subProxy:n,_stack:new Set,_descriptors:eg(t,r),setContext:s=>Rr(t,s,n,r),override:s=>Rr(t.override(s),e,n,r)};return new Proxy(i,{deleteProperty(s,a){return delete s[a],delete t[a],!0},get(s,a,o){return tg(s,a,()=>Hb(s,a,o))},getOwnPropertyDescriptor(s,a){return s._descriptors.allKeys?Reflect.has(t,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,a)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,a){return Reflect.has(t,a)},ownKeys(){return Reflect.ownKeys(t)},set(s,a,o){return t[a]=o,delete s[a],!0}})}function eg(t,e={scriptable:!0,indexable:!0}){const{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:i=e.allKeys}=t;return{allKeys:i,scriptable:n,indexable:r,isScriptable:bn(n)?n:()=>n,isIndexable:bn(r)?r:()=>r}}const Ub=(t,e)=>t?t+ru(e):e,du=(t,e)=>B(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function tg(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const r=n();return t[e]=r,r}function Hb(t,e,n){const{_proxy:r,_context:i,_subProxy:s,_descriptors:a}=t;let o=r[e];return bn(o)&&a.isScriptable(e)&&(o=Bb(e,o,t,n)),ge(o)&&o.length&&(o=Wb(e,o,t,a.isIndexable)),du(e,o)&&(o=Rr(o,i,s&&s[e],a)),o}function Bb(t,e,n,r){const{_proxy:i,_context:s,_subProxy:a,_stack:o}=n;if(o.has(t))throw new Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(s,a||r);return o.delete(t),du(t,l)&&(l=hu(i._scopes,i,t,l)),l}function Wb(t,e,n,r){const{_proxy:i,_context:s,_subProxy:a,_descriptors:o}=n;if(typeof s.index<"u"&&r(t))return e[s.index%e.length];if(B(e[0])){const l=e,u=i._scopes.filter(d=>d!==l);e=[];for(const d of l){const h=hu(u,i,t,d);e.push(Rr(h,s,a&&a[t],o))}}return e}function ng(t,e,n){return bn(t)?t(e,n):t}const Vb=(t,e)=>t===!0?e:typeof t=="string"?Qn(e,t):void 0;function Yb(t,e,n,r,i){for(const s of e){const a=Vb(n,s);if(a){t.add(a);const o=ng(a._fallback,n,i);if(typeof o<"u"&&o!==n&&o!==r)return o}else if(a===!1&&typeof r<"u"&&n!==r)return null}return!1}function hu(t,e,n,r){const i=e._rootScopes,s=ng(e._fallback,n,r),a=[...t,...i],o=new Set;o.add(r);let l=sh(o,a,n,s||n,r);return l===null||typeof s<"u"&&s!==n&&(l=sh(o,a,s,l,r),l===null)?!1:uu(Array.from(o),[""],i,s,()=>Xb(e,n,r))}function sh(t,e,n,r,i){for(;n;)n=Yb(t,e,n,r,i);return n}function Xb(t,e,n){const r=t._getTarget();e in r||(r[e]={});const i=r[e];return ge(i)&&B(n)?n:i||{}}function Qb(t,e,n,r){let i;for(const s of e)if(i=rg(Ub(s,t),n),typeof i<"u")return du(t,i)?hu(n,r,t,i):i}function rg(t,e){for(const n of e){if(!n)continue;const r=n[t];if(typeof r<"u")return r}}function ah(t){let e=t._keys;return e||(e=t._keys=Kb(t._scopes)),e}function Kb(t){const e=new Set;for(const n of t)for(const r of Object.keys(n).filter(i=>!i.startsWith("_")))e.add(r);return Array.from(e)}function fu(){return typeof window<"u"&&typeof document<"u"}function mu(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function Oa(t,e,n){let r;return typeof t=="string"?(r=parseInt(t,10),t.indexOf("%")!==-1&&(r=r/100*e.parentNode[n])):r=t,r}const so=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function Gb(t,e){return so(t).getPropertyValue(e)}const qb=["top","right","bottom","left"];function Un(t,e,n){const r={};n=n?"-"+n:"";for(let i=0;i<4;i++){const s=qb[i];r[s]=parseFloat(t[e+"-"+s+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}const Zb=(t,e,n)=>(t>0||e>0)&&(!n||!n.shadowRoot);function Jb(t,e){const n=t.touches,r=n&&n.length?n[0]:t,{offsetX:i,offsetY:s}=r;let a=!1,o,l;if(Zb(i,s,t.target))o=i,l=s;else{const u=e.getBoundingClientRect();o=r.clientX-u.left,l=r.clientY-u.top,a=!0}return{x:o,y:l,box:a}}function Dn(t,e){if("native"in t)return t;const{canvas:n,currentDevicePixelRatio:r}=e,i=so(n),s=i.boxSizing==="border-box",a=Un(i,"padding"),o=Un(i,"border","width"),{x:l,y:u,box:d}=Jb(t,n),h=a.left+(d&&o.left),f=a.top+(d&&o.top);let{width:m,height:x}=e;return s&&(m-=a.width+o.width,x-=a.height+o.height),{x:Math.round((l-h)/m*n.width/r),y:Math.round((u-f)/x*n.height/r)}}function ew(t,e,n){let r,i;if(e===void 0||n===void 0){const s=t&&mu(t);if(!s)e=t.clientWidth,n=t.clientHeight;else{const a=s.getBoundingClientRect(),o=so(s),l=Un(o,"border","width"),u=Un(o,"padding");e=a.width-u.width-l.width,n=a.height-u.height-l.height,r=Oa(o.maxWidth,s,"clientWidth"),i=Oa(o.maxHeight,s,"clientHeight")}}return{width:e,height:n,maxWidth:r||Ea,maxHeight:i||Ea}}const Cs=t=>Math.round(t*10)/10;function tw(t,e,n,r){const i=so(t),s=Un(i,"margin"),a=Oa(i.maxWidth,t,"clientWidth")||Ea,o=Oa(i.maxHeight,t,"clientHeight")||Ea,l=ew(t,e,n);let{width:u,height:d}=l;if(i.boxSizing==="content-box"){const f=Un(i,"border","width"),m=Un(i,"padding");u-=m.width+f.width,d-=m.height+f.height}return u=Math.max(0,u-s.width),d=Math.max(0,r?u/r:d-s.height),u=Cs(Math.min(u,a,l.maxWidth)),d=Cs(Math.min(d,o,l.maxHeight)),u&&!d&&(d=Cs(u/2)),(e!==void 0||n!==void 0)&&r&&l.height&&d>l.height&&(d=l.height,u=Cs(Math.floor(d*r))),{width:u,height:d}}function oh(t,e,n){const r=e||1,i=Math.floor(t.height*r),s=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const a=t.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),t.currentDevicePixelRatio!==r||a.height!==i||a.width!==s?(t.currentDevicePixelRatio=r,a.height=i,a.width=s,t.ctx.setTransform(r,0,0,r,0,0),!0):!1}const nw=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};fu()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function lh(t,e){const n=Gb(t,e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}const rw=function(t,e){return{x(n){return t+t+e-n},setWidth(n){e=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,r){return n-r},leftForLtr(n,r){return n-r}}},iw=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function _r(t,e,n){return t?rw(e,n):iw()}function ig(t,e){let n,r;(e==="ltr"||e==="rtl")&&(n=t.canvas.style,r=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function sg(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Ps(t,e,n){return t.options.clip?t[n]:e[n]}function sw(t,e){const{xScale:n,yScale:r}=t;return n&&r?{left:Ps(n,e,"left"),right:Ps(n,e,"right"),top:Ps(r,e,"top"),bottom:Ps(r,e,"bottom")}:e}function aw(t,e){const n=e._clip;if(n.disabled)return!1;const r=sw(e,t.chartArea);return{left:n.left===!1?0:r.left-(n.left===!0?0:n.left),right:n.right===!1?t.width:r.right+(n.right===!0?0:n.right),top:n.top===!1?0:r.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?t.height:r.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class ow{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,n,r,i){const s=n.listeners[i],a=n.duration;s.forEach(o=>o({chart:e,initial:n.initial,numSteps:a,currentStep:Math.min(r-n.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Xp.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let n=0;this._charts.forEach((r,i)=>{if(!r.running||!r.items.length)return;const s=r.items;let a=s.length-1,o=!1,l;for(;a>=0;--a)l=s[a],l._active?(l._total>r.duration&&(r.duration=l._total),l.tick(e),o=!0):(s[a]=s[s.length-1],s.pop());o&&(i.draw(),this._notify(i,r,e,"progress")),s.length||(r.running=!1,this._notify(i,r,e,"complete"),r.initial=!1),n+=s.length}),this._lastDate=e,n===0&&(this._running=!1)}_getAnims(e){const n=this._charts;let r=n.get(e);return r||(r={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(e,r)),r}listen(e,n,r){this._getAnims(e).listeners[n].push(r)}add(e,n){!n||!n.length||this._getAnims(e).items.push(...n)}has(e){return this._getAnims(e).items.length>0}start(e){const n=this._charts.get(e);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((r,i)=>Math.max(r,i._duration),0),this._refresh())}running(e){if(!this._running)return!1;const n=this._charts.get(e);return!(!n||!n.running||!n.items.length)}stop(e){const n=this._charts.get(e);if(!n||!n.items.length)return;const r=n.items;let i=r.length-1;for(;i>=0;--i)r[i].cancel();n.items=[],this._notify(e,n,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Tt=new ow;const ch="transparent",lw={boolean(t,e,n){return n>.5?e:t},color(t,e,n){const r=eh(t||ch),i=r.valid&&eh(e||ch);return i&&i.valid?i.mix(r,n).hexString():e},number(t,e,n){return t+(e-t)*n}};class cw{constructor(e,n,r,i){const s=n[r];i=js([e.to,i,s,e.from]);const a=js([e.from,s,i]);this._active=!0,this._fn=e.fn||lw[e.type||typeof a],this._easing=Si[e.easing]||Si.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=n,this._prop=r,this._from=a,this._to=i,this._promises=void 0}active(){return this._active}update(e,n,r){if(this._active){this._notify(!1);const i=this._target[this._prop],s=r-this._start,a=this._duration-s;this._start=r,this._duration=Math.floor(Math.max(a,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=js([e.to,n,i,e.from]),this._from=js([e.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const n=e-this._start,r=this._duration,i=this._prop,s=this._from,a=this._loop,o=this._to;let l;if(this._active=s!==o&&(a||n<r),!this._active){this._target[i]=o,this._notify(!0);return}if(n<0){this._target[i]=s;return}l=n/r%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(s,o,l)}wait(){const e=this._promises||(this._promises=[]);return new Promise((n,r)=>{e.push({res:n,rej:r})})}_notify(e){const n=e?"res":"rej",r=this._promises||[];for(let i=0;i<r.length;i++)r[i][n]()}}class ag{constructor(e,n){this._chart=e,this._properties=new Map,this.configure(n)}configure(e){if(!B(e))return;const n=Object.keys(ue.animation),r=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const s=e[i];if(!B(s))return;const a={};for(const o of n)a[o]=s[o];(ge(s.properties)&&s.properties||[i]).forEach(o=>{(o===i||!r.has(o))&&r.set(o,a)})})}_animateOptions(e,n){const r=n.options,i=dw(e,r);if(!i)return[];const s=this._createAnimations(i,r);return r.$shared&&uw(e.options.$animations,r).then(()=>{e.options=r},()=>{}),s}_createAnimations(e,n){const r=this._properties,i=[],s=e.$animations||(e.$animations={}),a=Object.keys(n),o=Date.now();let l;for(l=a.length-1;l>=0;--l){const u=a[l];if(u.charAt(0)==="$")continue;if(u==="options"){i.push(...this._animateOptions(e,n));continue}const d=n[u];let h=s[u];const f=r.get(u);if(h)if(f&&h.active()){h.update(f,d,o);continue}else h.cancel();if(!f||!f.duration){e[u]=d;continue}s[u]=h=new cw(f,e,u,d),i.push(h)}return i}update(e,n){if(this._properties.size===0){Object.assign(e,n);return}const r=this._createAnimations(e,n);if(r.length)return Tt.add(this._chart,r),!0}}function uw(t,e){const n=[],r=Object.keys(e);for(let i=0;i<r.length;i++){const s=t[r[i]];s&&s.active()&&n.push(s.wait())}return Promise.all(n)}function dw(t,e){if(!e)return;let n=t.options;if(!n){t.options=e;return}return n.$shared&&(t.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function uh(t,e){const n=t&&t.options||{},r=n.reverse,i=n.min===void 0?e:0,s=n.max===void 0?e:0;return{start:r?s:i,end:r?i:s}}function hw(t,e,n){if(n===!1)return!1;const r=uh(t,n),i=uh(e,n);return{top:i.end,right:r.end,bottom:i.start,left:r.start}}function fw(t){let e,n,r,i;return B(t)?(e=t.top,n=t.right,r=t.bottom,i=t.left):e=n=r=i=t,{top:e,right:n,bottom:r,left:i,disabled:t===!1}}function og(t,e){const n=[],r=t._getSortedDatasetMetas(e);let i,s;for(i=0,s=r.length;i<s;++i)n.push(r[i].index);return n}function dh(t,e,n,r={}){const i=t.keys,s=r.mode==="single";let a,o,l,u;if(e===null)return;let d=!1;for(a=0,o=i.length;a<o;++a){if(l=+i[a],l===n){if(d=!0,r.all)continue;break}u=t.values[l],lt(u)&&(s||e===0||xn(e)===xn(u))&&(e+=u)}return!d&&!r.all?0:e}function mw(t,e){const{iScale:n,vScale:r}=e,i=n.axis==="x"?"x":"y",s=r.axis==="x"?"x":"y",a=Object.keys(t),o=new Array(a.length);let l,u,d;for(l=0,u=a.length;l<u;++l)d=a[l],o[l]={[i]:d,[s]:t[d]};return o}function Uo(t,e){const n=t&&t.options.stacked;return n||n===void 0&&e.stack!==void 0}function pw(t,e,n){return`${t.id}.${e.id}.${n.stack||n.type}`}function gw(t){const{min:e,max:n,minDefined:r,maxDefined:i}=t.getUserBounds();return{min:r?e:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function xw(t,e,n){const r=t[e]||(t[e]={});return r[n]||(r[n]={})}function hh(t,e,n,r){for(const i of e.getMatchingVisibleMetas(r).reverse()){const s=t[i.index];if(n&&s>0||!n&&s<0)return i.index}return null}function fh(t,e){const{chart:n,_cachedMeta:r}=t,i=n._stacks||(n._stacks={}),{iScale:s,vScale:a,index:o}=r,l=s.axis,u=a.axis,d=pw(s,a,r),h=e.length;let f;for(let m=0;m<h;++m){const x=e[m],{[l]:y,[u]:b}=x,g=x._stacks||(x._stacks={});f=g[u]=xw(i,d,y),f[o]=b,f._top=hh(f,a,!0,r.type),f._bottom=hh(f,a,!1,r.type);const p=f._visualValues||(f._visualValues={});p[o]=b}}function Ho(t,e){const n=t.scales;return Object.keys(n).filter(r=>n[r].axis===e).shift()}function yw(t,e){return Ir(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function vw(t,e,n){return Ir(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:n,index:e,mode:"default",type:"data"})}function Zr(t,e){const n=t.controller.index,r=t.vScale&&t.vScale.axis;if(r){e=e||t._parsed;for(const i of e){const s=i._stacks;if(!s||s[r]===void 0||s[r][n]===void 0)return;delete s[r][n],s[r]._visualValues!==void 0&&s[r]._visualValues[n]!==void 0&&delete s[r]._visualValues[n]}}}const Bo=t=>t==="reset"||t==="none",mh=(t,e)=>e?t:Object.assign({},t),bw=(t,e,n)=>t&&!e.hidden&&e._stacked&&{keys:og(n,!0),values:null};class Nr{constructor(e,n){this.chart=e,this._ctx=e.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Uo(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&Zr(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,n=this._cachedMeta,r=this.getDataset(),i=(h,f,m,x)=>h==="x"?f:h==="r"?x:m,s=n.xAxisID=W(r.xAxisID,Ho(e,"x")),a=n.yAxisID=W(r.yAxisID,Ho(e,"y")),o=n.rAxisID=W(r.rAxisID,Ho(e,"r")),l=n.indexAxis,u=n.iAxisID=i(l,s,a,o),d=n.vAxisID=i(l,a,s,o);n.xScale=this.getScaleForId(s),n.yScale=this.getScaleForId(a),n.rScale=this.getScaleForId(o),n.iScale=this.getScaleForId(u),n.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const n=this._cachedMeta;return e===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&qd(this._data,this),e._stacked&&Zr(e)}_dataCheck(){const e=this.getDataset(),n=e.data||(e.data=[]),r=this._data;if(B(n)){const i=this._cachedMeta;this._data=mw(n,i)}else if(r!==n){if(r){qd(r,this);const i=this._cachedMeta;Zr(i),i._parsed=[]}n&&Object.isExtensible(n)&&bb(n,this),this._syncList=[],this._data=n}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const n=this._cachedMeta,r=this.getDataset();let i=!1;this._dataCheck();const s=n._stacked;n._stacked=Uo(n.vScale,n),n.stack!==r.stack&&(i=!0,Zr(n),n.stack=r.stack),this._resyncElements(e),(i||s!==n._stacked)&&(fh(this,n._parsed),n._stacked=Uo(n.vScale,n))}configure(){const e=this.chart.config,n=e.datasetScopeKeys(this._type),r=e.getOptionScopes(this.getDataset(),n,!0);this.options=e.createResolver(r,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,n){const{_cachedMeta:r,_data:i}=this,{iScale:s,_stacked:a}=r,o=s.axis;let l=e===0&&n===i.length?!0:r._sorted,u=e>0&&r._parsed[e-1],d,h,f;if(this._parsing===!1)r._parsed=i,r._sorted=!0,f=i;else{ge(i[e])?f=this.parseArrayData(r,i,e,n):B(i[e])?f=this.parseObjectData(r,i,e,n):f=this.parsePrimitiveData(r,i,e,n);const m=()=>h[o]===null||u&&h[o]<u[o];for(d=0;d<n;++d)r._parsed[d+e]=h=f[d],l&&(m()&&(l=!1),u=h);r._sorted=l}a&&fh(this,f)}parsePrimitiveData(e,n,r,i){const{iScale:s,vScale:a}=e,o=s.axis,l=a.axis,u=s.getLabels(),d=s===a,h=new Array(i);let f,m,x;for(f=0,m=i;f<m;++f)x=f+r,h[f]={[o]:d||s.parse(u[x],x),[l]:a.parse(n[x],x)};return h}parseArrayData(e,n,r,i){const{xScale:s,yScale:a}=e,o=new Array(i);let l,u,d,h;for(l=0,u=i;l<u;++l)d=l+r,h=n[d],o[l]={x:s.parse(h[0],d),y:a.parse(h[1],d)};return o}parseObjectData(e,n,r,i){const{xScale:s,yScale:a}=e,{xAxisKey:o="x",yAxisKey:l="y"}=this._parsing,u=new Array(i);let d,h,f,m;for(d=0,h=i;d<h;++d)f=d+r,m=n[f],u[d]={x:s.parse(Qn(m,o),f),y:a.parse(Qn(m,l),f)};return u}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,n,r){const i=this.chart,s=this._cachedMeta,a=n[e.axis],o={keys:og(i,!0),values:n._stacks[e.axis]._visualValues};return dh(o,a,s.index,{mode:r})}updateRangeFromParsed(e,n,r,i){const s=r[n.axis];let a=s===null?NaN:s;const o=i&&r._stacks[n.axis];i&&o&&(i.values=o,a=dh(i,s,this._cachedMeta.index)),e.min=Math.min(e.min,a),e.max=Math.max(e.max,a)}getMinMax(e,n){const r=this._cachedMeta,i=r._parsed,s=r._sorted&&e===r.iScale,a=i.length,o=this._getOtherScale(e),l=bw(n,r,this.chart),u={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:h}=gw(o);let f,m;function x(){m=i[f];const y=m[o.axis];return!lt(m[e.axis])||d>y||h<y}for(f=0;f<a&&!(!x()&&(this.updateRangeFromParsed(u,e,m,l),s));++f);if(s){for(f=a-1;f>=0;--f)if(!x()){this.updateRangeFromParsed(u,e,m,l);break}}return u}getAllParsedValues(e){const n=this._cachedMeta._parsed,r=[];let i,s,a;for(i=0,s=n.length;i<s;++i)a=n[i][e.axis],lt(a)&&r.push(a);return r}getMaxOverflow(){return!1}getLabelAndValue(e){const n=this._cachedMeta,r=n.iScale,i=n.vScale,s=this.getParsed(e);return{label:r?""+r.getLabelForValue(s[r.axis]):"",value:i?""+i.getLabelForValue(s[i.axis]):""}}_update(e){const n=this._cachedMeta;this.update(e||"default"),n._clip=fw(W(this.options.clip,hw(n.xScale,n.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,n=this.chart,r=this._cachedMeta,i=r.data||[],s=n.chartArea,a=[],o=this._drawStart||0,l=this._drawCount||i.length-o,u=this.options.drawActiveElementsOnTop;let d;for(r.dataset&&r.dataset.draw(e,s,o,l),d=o;d<o+l;++d){const h=i[d];h.hidden||(h.active&&u?a.push(h):h.draw(e,s))}for(d=0;d<a.length;++d)a[d].draw(e,s)}getStyle(e,n){const r=n?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(r):this.resolveDataElementOptions(e||0,r)}getContext(e,n,r){const i=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const a=this._cachedMeta.data[e];s=a.$context||(a.$context=vw(this.getContext(),e,a)),s.parsed=this.getParsed(e),s.raw=i.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=yw(this.chart.getContext(),this.index)),s.dataset=i,s.index=s.datasetIndex=this.index;return s.active=!!n,s.mode=r,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,n){return this._resolveElementOptions(this.dataElementType.id,n,e)}_resolveElementOptions(e,n="default",r){const i=n==="active",s=this._cachedDataOpts,a=e+"-"+n,o=s[a],l=this.enableOptionSharing&&Yi(r);if(o)return mh(o,l);const u=this.chart.config,d=u.datasetElementScopeKeys(this._type,e),h=i?[`${e}Hover`,"hover",e,""]:[e,""],f=u.getOptionScopes(this.getDataset(),d),m=Object.keys(ue.elements[e]),x=()=>this.getContext(r,i,n),y=u.resolveNamedOptions(f,m,x,h);return y.$shared&&(y.$shared=l,s[a]=Object.freeze(mh(y,l))),y}_resolveAnimations(e,n,r){const i=this.chart,s=this._cachedDataOpts,a=`animation-${n}`,o=s[a];if(o)return o;let l;if(i.options.animation!==!1){const d=this.chart.config,h=d.datasetAnimationScopeKeys(this._type,n),f=d.getOptionScopes(this.getDataset(),h);l=d.createResolver(f,this.getContext(e,r,n))}const u=new ag(i,l&&l.animations);return l&&l._cacheable&&(s[a]=Object.freeze(u)),u}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,n){return!n||Bo(e)||this.chart._animationsDisabled}_getSharedOptions(e,n){const r=this.resolveDataElementOptions(e,n),i=this._sharedOptions,s=this.getSharedOptions(r),a=this.includeOptions(n,s)||s!==i;return this.updateSharedOptions(s,n,r),{sharedOptions:s,includeOptions:a}}updateElement(e,n,r,i){Bo(i)?Object.assign(e,r):this._resolveAnimations(n,i).update(e,r)}updateSharedOptions(e,n,r){e&&!Bo(n)&&this._resolveAnimations(void 0,n).update(e,r)}_setStyle(e,n,r,i){e.active=i;const s=this.getStyle(n,i);this._resolveAnimations(n,r,i).update(e,{options:!i&&this.getSharedOptions(s)||s})}removeHoverStyle(e,n,r){this._setStyle(e,r,"active",!1)}setHoverStyle(e,n,r){this._setStyle(e,r,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const n=this._data,r=this._cachedMeta.data;for(const[o,l,u]of this._syncList)this[o](l,u);this._syncList=[];const i=r.length,s=n.length,a=Math.min(s,i);a&&this.parse(0,a),s>i?this._insertElements(i,s-i,e):s<i&&this._removeElements(s,i-s)}_insertElements(e,n,r=!0){const i=this._cachedMeta,s=i.data,a=e+n;let o;const l=u=>{for(u.length+=n,o=u.length-1;o>=a;o--)u[o]=u[o-n]};for(l(s),o=e;o<a;++o)s[o]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(e,n),r&&this.updateElements(s,e,n,"reset")}updateElements(e,n,r,i){}_removeElements(e,n){const r=this._cachedMeta;if(this._parsing){const i=r._parsed.splice(e,n);r._stacked&&Zr(r,i)}r.data.splice(e,n)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[n,r,i]=e;this[n](r,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,n){n&&this._sync(["_removeElements",e,n]);const r=arguments.length-2;r&&this._sync(["_insertElements",e,r])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}F(Nr,"defaults",{}),F(Nr,"datasetElementType",null),F(Nr,"dataElementType",null);function ww(t,e){if(!t._cache.$bar){const n=t.getMatchingVisibleMetas(e);let r=[];for(let i=0,s=n.length;i<s;i++)r=r.concat(n[i].controller.getAllParsedValues(t));t._cache.$bar=Yp(r.sort((i,s)=>i-s))}return t._cache.$bar}function Sw(t){const e=t.iScale,n=ww(e,t.type);let r=e._length,i,s,a,o;const l=()=>{a===32767||a===-32768||(Yi(o)&&(r=Math.min(r,Math.abs(a-o)||r)),o=a)};for(i=0,s=n.length;i<s;++i)a=e.getPixelForValue(n[i]),l();for(o=void 0,i=0,s=e.ticks.length;i<s;++i)a=e.getPixelForTick(i),l();return r}function _w(t,e,n,r){const i=n.barThickness;let s,a;return q(i)?(s=e.min*n.categoryPercentage,a=n.barPercentage):(s=i*r,a=1),{chunk:s/r,ratio:a,start:e.pixels[t]-s/2}}function Nw(t,e,n,r){const i=e.pixels,s=i[t];let a=t>0?i[t-1]:null,o=t<i.length-1?i[t+1]:null;const l=n.categoryPercentage;a===null&&(a=s-(o===null?e.end-e.start:o-s)),o===null&&(o=s+s-a);const u=s-(s-Math.min(a,o))/2*l;return{chunk:Math.abs(o-a)/2*l/r,ratio:n.barPercentage,start:u}}function kw(t,e,n,r){const i=n.parse(t[0],r),s=n.parse(t[1],r),a=Math.min(i,s),o=Math.max(i,s);let l=a,u=o;Math.abs(a)>Math.abs(o)&&(l=o,u=a),e[n.axis]=u,e._custom={barStart:l,barEnd:u,start:i,end:s,min:a,max:o}}function lg(t,e,n,r){return ge(t)?kw(t,e,n,r):e[n.axis]=n.parse(t,r),e}function ph(t,e,n,r){const i=t.iScale,s=t.vScale,a=i.getLabels(),o=i===s,l=[];let u,d,h,f;for(u=n,d=n+r;u<d;++u)f=e[u],h={},h[i.axis]=o||i.parse(a[u],u),l.push(lg(f,h,s,u));return l}function Wo(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function jw(t,e,n){return t!==0?xn(t):(e.isHorizontal()?1:-1)*(e.min>=n?1:-1)}function Cw(t){let e,n,r,i,s;return t.horizontal?(e=t.base>t.x,n="left",r="right"):(e=t.base<t.y,n="bottom",r="top"),e?(i="end",s="start"):(i="start",s="end"),{start:n,end:r,reverse:e,top:i,bottom:s}}function Pw(t,e,n,r){let i=e.borderSkipped;const s={};if(!i){t.borderSkipped=s;return}if(i===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:a,end:o,reverse:l,top:u,bottom:d}=Cw(t);i==="middle"&&n&&(t.enableBorderRadius=!0,(n._top||0)===r?i=u:(n._bottom||0)===r?i=d:(s[gh(d,a,o,l)]=!0,i=u)),s[gh(i,a,o,l)]=!0,t.borderSkipped=s}function gh(t,e,n,r){return r?(t=Mw(t,e,n),t=xh(t,n,e)):t=xh(t,e,n),t}function Mw(t,e,n){return t===e?n:t===n?e:t}function xh(t,e,n){return t==="start"?e:t==="end"?n:t}function $w(t,{inflateAmount:e},n){t.inflateAmount=e==="auto"?n===1?.33:0:e}class Qs extends Nr{parsePrimitiveData(e,n,r,i){return ph(e,n,r,i)}parseArrayData(e,n,r,i){return ph(e,n,r,i)}parseObjectData(e,n,r,i){const{iScale:s,vScale:a}=e,{xAxisKey:o="x",yAxisKey:l="y"}=this._parsing,u=s.axis==="x"?o:l,d=a.axis==="x"?o:l,h=[];let f,m,x,y;for(f=r,m=r+i;f<m;++f)y=n[f],x={},x[s.axis]=s.parse(Qn(y,u),f),h.push(lg(Qn(y,d),x,a,f));return h}updateRangeFromParsed(e,n,r,i){super.updateRangeFromParsed(e,n,r,i);const s=r._custom;s&&n===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const n=this._cachedMeta,{iScale:r,vScale:i}=n,s=this.getParsed(e),a=s._custom,o=Wo(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+r.getLabelForValue(s[r.axis]),value:o}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,e)}updateElements(e,n,r,i){const s=i==="reset",{index:a,_cachedMeta:{vScale:o}}=this,l=o.getBasePixel(),u=o.isHorizontal(),d=this._getRuler(),{sharedOptions:h,includeOptions:f}=this._getSharedOptions(n,i);for(let m=n;m<n+r;m++){const x=this.getParsed(m),y=s||q(x[o.axis])?{base:l,head:l}:this._calculateBarValuePixels(m),b=this._calculateBarIndexPixels(m,d),g=(x._stacks||{})[o.axis],p={horizontal:u,base:y.base,enableBorderRadius:!g||Wo(x._custom)||a===g._top||a===g._bottom,x:u?y.head:b.center,y:u?b.center:y.head,height:u?b.size:Math.abs(y.size),width:u?Math.abs(y.size):b.size};f&&(p.options=h||this.resolveDataElementOptions(m,e[m].active?"active":i));const v=p.options||e[m].options;Pw(p,v,g,a),$w(p,v,d.ratio),this.updateElement(e[m],m,p,i)}}_getStacks(e,n){const{iScale:r}=this._cachedMeta,i=r.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=r.options.stacked,a=[],o=this._cachedMeta.controller.getParsed(n),l=o&&o[r.axis],u=d=>{const h=d._parsed.find(m=>m[r.axis]===l),f=h&&h[d.vScale.axis];if(q(f)||isNaN(f))return!0};for(const d of i)if(!(n!==void 0&&u(d))&&((s===!1||a.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&a.push(d.stack),d.index===e))break;return a.length||a.push(void 0),a}_getStackCount(e){return this._getStacks(void 0,e).length}_getStackIndex(e,n,r){const i=this._getStacks(e,r),s=n!==void 0?i.indexOf(n):-1;return s===-1?i.length-1:s}_getRuler(){const e=this.options,n=this._cachedMeta,r=n.iScale,i=[];let s,a;for(s=0,a=n.data.length;s<a;++s)i.push(r.getPixelForValue(this.getParsed(s)[r.axis],s));const o=e.barThickness;return{min:o||Sw(n),pixels:i,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:e.grouped,ratio:o?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:n,_stacked:r,index:i},options:{base:s,minBarLength:a}}=this,o=s||0,l=this.getParsed(e),u=l._custom,d=Wo(u);let h=l[n.axis],f=0,m=r?this.applyStack(n,l,r):h,x,y;m!==h&&(f=m-h,m=h),d&&(h=u.barStart,m=u.barEnd-u.barStart,h!==0&&xn(h)!==xn(u.barEnd)&&(f=0),f+=h);const b=!q(s)&&!d?s:f;let g=n.getPixelForValue(b);if(this.chart.getDataVisibility(e)?x=n.getPixelForValue(f+m):x=g,y=x-g,Math.abs(y)<a){y=jw(y,n,o)*a,h===o&&(g-=y/2);const p=n.getPixelForDecimal(0),v=n.getPixelForDecimal(1),w=Math.min(p,v),_=Math.max(p,v);g=Math.max(Math.min(g,_),w),x=g+y,r&&!d&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(x)-n.getValueForPixel(g))}if(g===n.getPixelForValue(o)){const p=xn(y)*n.getLineWidthForValue(o)/2;g+=p,y-=p}return{size:y,base:g,head:x,center:x+y/2}}_calculateBarIndexPixels(e,n){const r=n.scale,i=this.options,s=i.skipNull,a=W(i.maxBarThickness,1/0);let o,l;if(n.grouped){const u=s?this._getStackCount(e):n.stackCount,d=i.barThickness==="flex"?Nw(e,n,i,u):_w(e,n,i,u),h=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0);o=d.start+d.chunk*h+d.chunk/2,l=Math.min(a,d.chunk*d.ratio)}else o=r.getPixelForValue(this.getParsed(e)[r.axis],e),l=Math.min(a,n.min*n.ratio);return{base:o-l/2,head:o+l/2,center:o,size:l}}draw(){const e=this._cachedMeta,n=e.vScale,r=e.data,i=r.length;let s=0;for(;s<i;++s)this.getParsed(s)[n.axis]!==null&&!r[s].hidden&&r[s].draw(this._ctx)}}F(Qs,"id","bar"),F(Qs,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),F(Qs,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function Ew(t,e,n){let r=1,i=1,s=0,a=0;if(e<he){const o=t,l=o+e,u=Math.cos(o),d=Math.sin(o),h=Math.cos(l),f=Math.sin(l),m=(v,w,_)=>Ra(v,o,l,!0)?1:Math.max(w,w*n,_,_*n),x=(v,w,_)=>Ra(v,o,l,!0)?-1:Math.min(w,w*n,_,_*n),y=m(0,u,h),b=m(ye,d,f),g=x(fe,u,h),p=x(fe+ye,d,f);r=(y-g)/2,i=(b-p)/2,s=-(y+g)/2,a=-(b+p)/2}return{ratioX:r,ratioY:i,offsetX:s,offsetY:a}}class li extends Nr{constructor(e,n){super(e,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,n){const r=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=r;else{let s=l=>+r[l];if(B(r[e])){const{key:l="value"}=this._parsing;s=u=>+Qn(r[u],l)}let a,o;for(a=e,o=e+n;a<o;++a)i._parsed[a]=s(a)}}_getRotation(){return At(this.options.rotation-90)}_getCircumference(){return At(this.options.circumference)}_getRotationExtents(){let e=he,n=-he;for(let r=0;r<this.chart.data.datasets.length;++r)if(this.chart.isDatasetVisible(r)&&this.chart.getDatasetMeta(r).type===this._type){const i=this.chart.getDatasetMeta(r).controller,s=i._getRotation(),a=i._getCircumference();e=Math.min(e,s),n=Math.max(n,s+a)}return{rotation:e,circumference:n-e}}update(e){const n=this.chart,{chartArea:r}=n,i=this._cachedMeta,s=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,o=Math.max((Math.min(r.width,r.height)-a)/2,0),l=Math.min(ib(this.options.cutout,o),1),u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:m,offsetX:x,offsetY:y}=Ew(h,d,l),b=(r.width-a)/f,g=(r.height-a)/m,p=Math.max(Math.min(b,g)/2,0),v=Up(this.options.radius,p),w=Math.max(v*l,0),_=(v-w)/this._getVisibleDatasetWeightTotal();this.offsetX=x*v,this.offsetY=y*v,i.total=this.calculateTotal(),this.outerRadius=v-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*u,0),this.updateElements(s,0,s.length,e)}_circumference(e,n){const r=this.options,i=this._cachedMeta,s=this._getCircumference();return n&&r.animation.animateRotate||!this.chart.getDataVisibility(e)||i._parsed[e]===null||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*s/he)}updateElements(e,n,r,i){const s=i==="reset",a=this.chart,o=a.chartArea,u=a.options.animation,d=(o.left+o.right)/2,h=(o.top+o.bottom)/2,f=s&&u.animateScale,m=f?0:this.innerRadius,x=f?0:this.outerRadius,{sharedOptions:y,includeOptions:b}=this._getSharedOptions(n,i);let g=this._getRotation(),p;for(p=0;p<n;++p)g+=this._circumference(p,s);for(p=n;p<n+r;++p){const v=this._circumference(p,s),w=e[p],_={x:d+this.offsetX,y:h+this.offsetY,startAngle:g,endAngle:g+v,circumference:v,outerRadius:x,innerRadius:m};b&&(_.options=y||this.resolveDataElementOptions(p,w.active?"active":i)),g+=v,this.updateElement(w,p,_,i)}}calculateTotal(){const e=this._cachedMeta,n=e.data;let r=0,i;for(i=0;i<n.length;i++){const s=e._parsed[i];s!==null&&!isNaN(s)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(r+=Math.abs(s))}return r}calculateCircumference(e){const n=this._cachedMeta.total;return n>0&&!isNaN(e)?he*(Math.abs(e)/n):0}getLabelAndValue(e){const n=this._cachedMeta,r=this.chart,i=r.data.labels||[],s=au(n._parsed[e],r.options.locale);return{label:i[e]||"",value:s}}getMaxBorderWidth(e){let n=0;const r=this.chart;let i,s,a,o,l;if(!e){for(i=0,s=r.data.datasets.length;i<s;++i)if(r.isDatasetVisible(i)){a=r.getDatasetMeta(i),e=a.data,o=a.controller;break}}if(!e)return 0;for(i=0,s=e.length;i<s;++i)l=o.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(e){let n=0;for(let r=0,i=e.length;r<i;++r){const s=this.resolveDataElementOptions(r);n=Math.max(n,s.offset||0,s.hoverOffset||0)}return n}_getRingWeightOffset(e){let n=0;for(let r=0;r<e;++r)this.chart.isDatasetVisible(r)&&(n+=this._getRingWeight(r));return n}_getRingWeight(e){return Math.max(W(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}F(li,"id","doughnut"),F(li,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),F(li,"descriptors",{_scriptable:e=>e!=="spacing",_indexable:e=>e!=="spacing"&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),F(li,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const n=e.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:r,color:i}}=e.legend.options;return n.labels.map((s,a)=>{const l=e.getDatasetMeta(0).controller.getStyle(a);return{text:s,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:r,hidden:!e.getDataVisibility(a),index:a}})}return[]}},onClick(e,n,r){r.chart.toggleDataVisibility(n.index),r.chart.update()}}}});function Mn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class pu{constructor(e){F(this,"options");this.options=e||{}}static override(e){Object.assign(pu.prototype,e)}init(){}formats(){return Mn()}parse(){return Mn()}format(){return Mn()}add(){return Mn()}diff(){return Mn()}startOf(){return Mn()}endOf(){return Mn()}}var Tw={_date:pu};function Rw(t,e,n,r){const{controller:i,data:s,_sorted:a}=t,o=i._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&e!=="r"&&a&&s.length){const u=o._reversePixels?yb:ql;if(r){if(i._sharedOptions){const d=s[0],h=typeof d.getRange=="function"&&d.getRange(e);if(h){const f=u(s,e,n-h),m=u(s,e,n+h);return{lo:f.lo,hi:m.hi}}}}else{const d=u(s,e,n);if(l){const{vScale:h}=i._cachedMeta,{_parsed:f}=t,m=f.slice(0,d.lo+1).reverse().findIndex(y=>!q(y[h.axis]));d.lo-=Math.max(0,m);const x=f.slice(d.hi).findIndex(y=>!q(y[h.axis]));d.hi+=Math.max(0,x)}return d}}return{lo:0,hi:s.length-1}}function ao(t,e,n,r,i){const s=t.getSortedVisibleDatasetMetas(),a=n[e];for(let o=0,l=s.length;o<l;++o){const{index:u,data:d}=s[o],{lo:h,hi:f}=Rw(s[o],e,a,i);for(let m=h;m<=f;++m){const x=d[m];x.skip||r(x,u,m)}}}function Dw(t){const e=t.indexOf("x")!==-1,n=t.indexOf("y")!==-1;return function(r,i){const s=e?Math.abs(r.x-i.x):0,a=n?Math.abs(r.y-i.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(a,2))}}function Vo(t,e,n,r,i){const s=[];return!i&&!t.isPointInArea(e)||ao(t,n,e,function(o,l,u){!i&&!Zp(o,t.chartArea,0)||o.inRange(e.x,e.y,r)&&s.push({element:o,datasetIndex:l,index:u})},!0),s}function Ow(t,e,n,r){let i=[];function s(a,o,l){const{startAngle:u,endAngle:d}=a.getProps(["startAngle","endAngle"],r),{angle:h}=Wp(a,{x:e.x,y:e.y});Ra(h,u,d)&&i.push({element:a,datasetIndex:o,index:l})}return ao(t,n,e,s),i}function Lw(t,e,n,r,i,s){let a=[];const o=Dw(n);let l=Number.POSITIVE_INFINITY;function u(d,h,f){const m=d.inRange(e.x,e.y,i);if(r&&!m)return;const x=d.getCenterPoint(i);if(!(!!s||t.isPointInArea(x))&&!m)return;const b=o(e,x);b<l?(a=[{element:d,datasetIndex:h,index:f}],l=b):b===l&&a.push({element:d,datasetIndex:h,index:f})}return ao(t,n,e,u),a}function Yo(t,e,n,r,i,s){return!s&&!t.isPointInArea(e)?[]:n==="r"&&!r?Ow(t,e,n,i):Lw(t,e,n,r,i,s)}function yh(t,e,n,r,i){const s=[],a=n==="x"?"inXRange":"inYRange";let o=!1;return ao(t,n,e,(l,u,d)=>{l[a]&&l[a](e[n],i)&&(s.push({element:l,datasetIndex:u,index:d}),o=o||l.inRange(e.x,e.y,i))}),r&&!o?[]:s}var zw={modes:{index(t,e,n,r){const i=Dn(e,t),s=n.axis||"x",a=n.includeInvisible||!1,o=n.intersect?Vo(t,i,s,r,a):Yo(t,i,s,!1,r,a),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(u=>{const d=o[0].index,h=u.data[d];h&&!h.skip&&l.push({element:h,datasetIndex:u.index,index:d})}),l):[]},dataset(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;let o=n.intersect?Vo(t,i,s,r,a):Yo(t,i,s,!1,r,a);if(o.length>0){const l=o[0].datasetIndex,u=t.getDatasetMeta(l).data;o=[];for(let d=0;d<u.length;++d)o.push({element:u[d],datasetIndex:l,index:d})}return o},point(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;return Vo(t,i,s,r,a)},nearest(t,e,n,r){const i=Dn(e,t),s=n.axis||"xy",a=n.includeInvisible||!1;return Yo(t,i,s,n.intersect,r,a)},x(t,e,n,r){const i=Dn(e,t);return yh(t,i,"x",n.intersect,r)},y(t,e,n,r){const i=Dn(e,t);return yh(t,i,"y",n.intersect,r)}}};const cg=["left","top","right","bottom"];function Jr(t,e){return t.filter(n=>n.pos===e)}function vh(t,e){return t.filter(n=>cg.indexOf(n.pos)===-1&&n.box.axis===e)}function ei(t,e){return t.sort((n,r)=>{const i=e?r:n,s=e?n:r;return i.weight===s.weight?i.index-s.index:i.weight-s.weight})}function Fw(t){const e=[];let n,r,i,s,a,o;for(n=0,r=(t||[]).length;n<r;++n)i=t[n],{position:s,options:{stack:a,stackWeight:o=1}}=i,e.push({index:n,box:i,pos:s,horizontal:i.isHorizontal(),weight:i.weight,stack:a&&s+a,stackWeight:o});return e}function Aw(t){const e={};for(const n of t){const{stack:r,pos:i,stackWeight:s}=n;if(!r||!cg.includes(i))continue;const a=e[r]||(e[r]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=s}return e}function Iw(t,e){const n=Aw(t),{vBoxMaxWidth:r,hBoxMaxHeight:i}=e;let s,a,o;for(s=0,a=t.length;s<a;++s){o=t[s];const{fullSize:l}=o.box,u=n[o.stack],d=u&&o.stackWeight/u.weight;o.horizontal?(o.width=d?d*r:l&&e.availableWidth,o.height=i):(o.width=r,o.height=d?d*i:l&&e.availableHeight)}return n}function Uw(t){const e=Fw(t),n=ei(e.filter(u=>u.box.fullSize),!0),r=ei(Jr(e,"left"),!0),i=ei(Jr(e,"right")),s=ei(Jr(e,"top"),!0),a=ei(Jr(e,"bottom")),o=vh(e,"x"),l=vh(e,"y");return{fullSize:n,leftAndTop:r.concat(s),rightAndBottom:i.concat(l).concat(a).concat(o),chartArea:Jr(e,"chartArea"),vertical:r.concat(i).concat(l),horizontal:s.concat(a).concat(o)}}function bh(t,e,n,r){return Math.max(t[n],e[n])+Math.max(t[r],e[r])}function ug(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function Hw(t,e,n,r){const{pos:i,box:s}=n,a=t.maxPadding;if(!B(i)){n.size&&(t[i]-=n.size);const h=r[n.stack]||{size:0,count:1};h.size=Math.max(h.size,n.horizontal?s.height:s.width),n.size=h.size/h.count,t[i]+=n.size}s.getPadding&&ug(a,s.getPadding());const o=Math.max(0,e.outerWidth-bh(a,t,"left","right")),l=Math.max(0,e.outerHeight-bh(a,t,"top","bottom")),u=o!==t.w,d=l!==t.h;return t.w=o,t.h=l,n.horizontal?{same:u,other:d}:{same:d,other:u}}function Bw(t){const e=t.maxPadding;function n(r){const i=Math.max(e[r]-t[r],0);return t[r]+=i,i}t.y+=n("top"),t.x+=n("left"),n("right"),n("bottom")}function Ww(t,e){const n=e.maxPadding;function r(i){const s={left:0,top:0,right:0,bottom:0};return i.forEach(a=>{s[a]=Math.max(e[a],n[a])}),s}return r(t?["left","right"]:["top","bottom"])}function ci(t,e,n,r){const i=[];let s,a,o,l,u,d;for(s=0,a=t.length,u=0;s<a;++s){o=t[s],l=o.box,l.update(o.width||e.w,o.height||e.h,Ww(o.horizontal,e));const{same:h,other:f}=Hw(e,n,o,r);u|=h&&i.length,d=d||f,l.fullSize||i.push(o)}return u&&ci(i,e,n,r)||d}function Ms(t,e,n,r,i){t.top=n,t.left=e,t.right=e+r,t.bottom=n+i,t.width=r,t.height=i}function wh(t,e,n,r){const i=n.padding;let{x:s,y:a}=e;for(const o of t){const l=o.box,u=r[o.stack]||{placed:0,weight:1},d=o.stackWeight/u.weight||1;if(o.horizontal){const h=e.w*d,f=u.size||l.height;Yi(u.start)&&(a=u.start),l.fullSize?Ms(l,i.left,a,n.outerWidth-i.right-i.left,f):Ms(l,e.left+u.placed,a,h,f),u.start=a,u.placed+=h,a=l.bottom}else{const h=e.h*d,f=u.size||l.width;Yi(u.start)&&(s=u.start),l.fullSize?Ms(l,s,i.top,f,n.outerHeight-i.bottom-i.top):Ms(l,s,e.top+u.placed,f,h),u.start=s,u.placed+=h,s=l.right}}e.x=s,e.y=a}var it={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(n){e.draw(n)}}]},t.boxes.push(e)},removeBox(t,e){const n=t.boxes?t.boxes.indexOf(e):-1;n!==-1&&t.boxes.splice(n,1)},configure(t,e,n){e.fullSize=n.fullSize,e.position=n.position,e.weight=n.weight},update(t,e,n,r){if(!t)return;const i=ct(t.options.layout.padding),s=Math.max(e-i.width,0),a=Math.max(n-i.height,0),o=Uw(t.boxes),l=o.vertical,u=o.horizontal;X(t.boxes,y=>{typeof y.beforeLayout=="function"&&y.beforeLayout()});const d=l.reduce((y,b)=>b.box.options&&b.box.options.display===!1?y:y+1,0)||1,h=Object.freeze({outerWidth:e,outerHeight:n,padding:i,availableWidth:s,availableHeight:a,vBoxMaxWidth:s/2/d,hBoxMaxHeight:a/2}),f=Object.assign({},i);ug(f,ct(r));const m=Object.assign({maxPadding:f,w:s,h:a,x:i.left,y:i.top},i),x=Iw(l.concat(u),h);ci(o.fullSize,m,h,x),ci(l,m,h,x),ci(u,m,h,x)&&ci(l,m,h,x),Bw(m),wh(o.leftAndTop,m,h,x),m.x+=m.w,m.y+=m.h,wh(o.rightAndBottom,m,h,x),t.chartArea={left:m.left,top:m.top,right:m.left+m.w,bottom:m.top+m.h,height:m.h,width:m.w},X(o.chartArea,y=>{const b=y.box;Object.assign(b,t.chartArea),b.update(m.w,m.h,{left:0,top:0,right:0,bottom:0})})}};class dg{acquireContext(e,n){}releaseContext(e){return!1}addEventListener(e,n,r){}removeEventListener(e,n,r){}getDevicePixelRatio(){return 1}getMaximumSize(e,n,r,i){return n=Math.max(0,n||e.width),r=r||e.height,{width:n,height:Math.max(0,i?Math.floor(n/i):r)}}isAttached(e){return!0}updateConfig(e){}}class Vw extends dg{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const Ks="$chartjs",Yw={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Sh=t=>t===null||t==="";function Xw(t,e){const n=t.style,r=t.getAttribute("height"),i=t.getAttribute("width");if(t[Ks]={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",Sh(i)){const s=lh(t,"width");s!==void 0&&(t.width=s)}if(Sh(r))if(t.style.height==="")t.height=t.width/(e||2);else{const s=lh(t,"height");s!==void 0&&(t.height=s)}return t}const hg=nw?{passive:!0}:!1;function Qw(t,e,n){t&&t.addEventListener(e,n,hg)}function Kw(t,e,n){t&&t.canvas&&t.canvas.removeEventListener(e,n,hg)}function Gw(t,e){const n=Yw[t.type]||t.type,{x:r,y:i}=Dn(t,e);return{type:n,chart:e,native:t,x:r!==void 0?r:null,y:i!==void 0?i:null}}function La(t,e){for(const n of t)if(n===e||n.contains(e))return!0}function qw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let a=!1;for(const o of s)a=a||La(o.addedNodes,r),a=a&&!La(o.removedNodes,r);a&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function Zw(t,e,n){const r=t.canvas,i=new MutationObserver(s=>{let a=!1;for(const o of s)a=a||La(o.removedNodes,r),a=a&&!La(o.addedNodes,r);a&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const Qi=new Map;let _h=0;function fg(){const t=window.devicePixelRatio;t!==_h&&(_h=t,Qi.forEach((e,n)=>{n.currentDevicePixelRatio!==t&&e()}))}function Jw(t,e){Qi.size||window.addEventListener("resize",fg),Qi.set(t,e)}function e2(t){Qi.delete(t),Qi.size||window.removeEventListener("resize",fg)}function t2(t,e,n){const r=t.canvas,i=r&&mu(r);if(!i)return;const s=Qp((o,l)=>{const u=i.clientWidth;n(o,l),u<i.clientWidth&&n()},window),a=new ResizeObserver(o=>{const l=o[0],u=l.contentRect.width,d=l.contentRect.height;u===0&&d===0||s(u,d)});return a.observe(i),Jw(t,s),a}function Xo(t,e,n){n&&n.disconnect(),e==="resize"&&e2(t)}function n2(t,e,n){const r=t.canvas,i=Qp(s=>{t.ctx!==null&&n(Gw(s,t))},t);return Qw(r,e,i),i}class r2 extends dg{acquireContext(e,n){const r=e&&e.getContext&&e.getContext("2d");return r&&r.canvas===e?(Xw(e,n),r):null}releaseContext(e){const n=e.canvas;if(!n[Ks])return!1;const r=n[Ks].initial;["height","width"].forEach(s=>{const a=r[s];q(a)?n.removeAttribute(s):n.setAttribute(s,a)});const i=r.style||{};return Object.keys(i).forEach(s=>{n.style[s]=i[s]}),n.width=n.width,delete n[Ks],!0}addEventListener(e,n,r){this.removeEventListener(e,n);const i=e.$proxies||(e.$proxies={}),a={attach:qw,detach:Zw,resize:t2}[n]||n2;i[n]=a(e,n,r)}removeEventListener(e,n){const r=e.$proxies||(e.$proxies={}),i=r[n];if(!i)return;({attach:Xo,detach:Xo,resize:Xo}[n]||Kw)(e,n,i),r[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,n,r,i){return tw(e,n,r,i)}isAttached(e){const n=e&&mu(e);return!!(n&&n.isConnected)}}function i2(t){return!fu()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?Vw:r2}class Vt{constructor(){F(this,"x");F(this,"y");F(this,"active",!1);F(this,"options");F(this,"$animations")}tooltipPosition(e){const{x:n,y:r}=this.getProps(["x","y"],e);return{x:n,y:r}}hasValue(){return Ta(this.x)&&Ta(this.y)}getProps(e,n){const r=this.$animations;if(!n||!r)return this;const i={};return e.forEach(s=>{i[s]=r[s]&&r[s].active()?r[s]._to:this[s]}),i}}F(Vt,"defaults",{}),F(Vt,"defaultRoutes");function s2(t,e){const n=t.options.ticks,r=a2(t),i=Math.min(n.maxTicksLimit||r,r),s=n.major.enabled?l2(e):[],a=s.length,o=s[0],l=s[a-1],u=[];if(a>i)return c2(e,u,s,a/i),u;const d=o2(s,e,i);if(a>0){let h,f;const m=a>1?Math.round((l-o)/(a-1)):null;for($s(e,u,d,q(m)?0:o-m,o),h=0,f=a-1;h<f;h++)$s(e,u,d,s[h],s[h+1]);return $s(e,u,d,l,q(m)?e.length:l+m),u}return $s(e,u,d),u}function a2(t){const e=t.options.offset,n=t._tickSize(),r=t._length/n+(e?0:1),i=t._maxLength/n;return Math.floor(Math.min(r,i))}function o2(t,e,n){const r=u2(t),i=e.length/n;if(!r)return Math.max(i,1);const s=db(r);for(let a=0,o=s.length-1;a<o;a++){const l=s[a];if(l>i)return l}return Math.max(i,1)}function l2(t){const e=[];let n,r;for(n=0,r=t.length;n<r;n++)t[n].major&&e.push(n);return e}function c2(t,e,n,r){let i=0,s=n[0],a;for(r=Math.ceil(r),a=0;a<t.length;a++)a===s&&(e.push(t[a]),i++,s=n[i*r])}function $s(t,e,n,r,i){const s=W(r,0),a=Math.min(W(i,t.length),t.length);let o=0,l,u,d;for(n=Math.ceil(n),i&&(l=i-r,n=l/Math.floor(l/n)),d=s;d<0;)o++,d=Math.round(s+o*n);for(u=Math.max(s,0);u<a;u++)u===d&&(e.push(t[u]),o++,d=Math.round(s+o*n))}function u2(t){const e=t.length;let n,r;if(e<2)return!1;for(r=t[0],n=1;n<e;++n)if(t[n]-t[n-1]!==r)return!1;return r}const d2=t=>t==="left"?"right":t==="right"?"left":t,Nh=(t,e,n)=>e==="top"||e==="left"?t[e]+n:t[e]-n,kh=(t,e)=>Math.min(e||t,t);function jh(t,e){const n=[],r=t.length/e,i=t.length;let s=0;for(;s<i;s+=r)n.push(t[Math.floor(s)]);return n}function h2(t,e,n){const r=t.ticks.length,i=Math.min(e,r-1),s=t._startPixel,a=t._endPixel,o=1e-6;let l=t.getPixelForTick(i),u;if(!(n&&(r===1?u=Math.max(l-s,a-l):e===0?u=(t.getPixelForTick(1)-l)/2:u=(l-t.getPixelForTick(i-1))/2,l+=i<e?u:-u,l<s-o||l>a+o)))return l}function f2(t,e){X(t,n=>{const r=n.gc,i=r.length/2;let s;if(i>e){for(s=0;s<i;++s)delete n.data[r[s]];r.splice(0,i)}})}function ti(t){return t.drawTicks?t.tickLength:0}function Ch(t,e){if(!t.display)return 0;const n=Pe(t.font,e),r=ct(t.padding);return(ge(t.text)?t.text.length:1)*n.lineHeight+r.height}function m2(t,e){return Ir(t,{scale:e,type:"scale"})}function p2(t,e,n){return Ir(t,{tick:n,index:e,type:"tick"})}function g2(t,e,n){let r=su(t);return(n&&e!=="right"||!n&&e==="right")&&(r=d2(r)),r}function x2(t,e,n,r){const{top:i,left:s,bottom:a,right:o,chart:l}=t,{chartArea:u,scales:d}=l;let h=0,f,m,x;const y=a-i,b=o-s;if(t.isHorizontal()){if(m=je(r,s,o),B(n)){const g=Object.keys(n)[0],p=n[g];x=d[g].getPixelForValue(p)+y-e}else n==="center"?x=(u.bottom+u.top)/2+y-e:x=Nh(t,n,e);f=o-s}else{if(B(n)){const g=Object.keys(n)[0],p=n[g];m=d[g].getPixelForValue(p)-b+e}else n==="center"?m=(u.left+u.right)/2-b+e:m=Nh(t,n,e);x=je(r,a,i),h=n==="left"?-ye:ye}return{titleX:m,titleY:x,maxWidth:f,rotation:h}}class Ur extends Vt{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,n){return e}getUserBounds(){let{_userMin:e,_userMax:n,_suggestedMin:r,_suggestedMax:i}=this;return e=St(e,Number.POSITIVE_INFINITY),n=St(n,Number.NEGATIVE_INFINITY),r=St(r,Number.POSITIVE_INFINITY),i=St(i,Number.NEGATIVE_INFINITY),{min:St(e,r),max:St(n,i),minDefined:lt(e),maxDefined:lt(n)}}getMinMax(e){let{min:n,max:r,minDefined:i,maxDefined:s}=this.getUserBounds(),a;if(i&&s)return{min:n,max:r};const o=this.getMatchingVisibleMetas();for(let l=0,u=o.length;l<u;++l)a=o[l].controller.getMinMax(this,e),i||(n=Math.min(n,a.min)),s||(r=Math.max(r,a.max));return n=s&&n>r?r:n,r=i&&n>r?n:r,{min:St(n,St(r,n)),max:St(r,St(n,r))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){te(this.options.beforeUpdate,[this])}update(e,n,r){const{beginAtZero:i,grace:s,ticks:a}=this.options,o=a.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=n,this._margins=r=Object.assign({left:0,right:0,top:0,bottom:0},r),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+r.left+r.right:this.height+r.top+r.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Ib(this,s,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=o<this.ticks.length;this._convertTicksToLabels(l?jh(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=s2(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,n,r;this.isHorizontal()?(n=this.left,r=this.right):(n=this.top,r=this.bottom,e=!e),this._startPixel=n,this._endPixel=r,this._reversePixels=e,this._length=r-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){te(this.options.afterUpdate,[this])}beforeSetDimensions(){te(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){te(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),te(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){te(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const n=this.options.ticks;let r,i,s;for(r=0,i=e.length;r<i;r++)s=e[r],s.label=te(n.callback,[s.value,r,e],this)}afterTickToLabelConversion(){te(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){te(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,n=e.ticks,r=kh(this.ticks.length,e.ticks.maxTicksLimit),i=n.minRotation||0,s=n.maxRotation;let a=i,o,l,u;if(!this._isVisible()||!n.display||i>=s||r<=1||!this.isHorizontal()){this.labelRotation=i;return}const d=this._getLabelSizes(),h=d.widest.width,f=d.highest.height,m=He(this.chart.width-h,0,this.maxWidth);o=e.offset?this.maxWidth/r:m/(r-1),h+6>o&&(o=m/(r-(e.offset?.5:1)),l=this.maxHeight-ti(e.grid)-n.padding-Ch(e.title,this.chart.options.font),u=Math.sqrt(h*h+f*f),a=pb(Math.min(Math.asin(He((d.highest.height+6)/o,-1,1)),Math.asin(He(l/u,-1,1))-Math.asin(He(f/u,-1,1)))),a=Math.max(i,Math.min(s,a))),this.labelRotation=a}afterCalculateLabelRotation(){te(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){te(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:n,options:{ticks:r,title:i,grid:s}}=this,a=this._isVisible(),o=this.isHorizontal();if(a){const l=Ch(i,n.options.font);if(o?(e.width=this.maxWidth,e.height=ti(s)+l):(e.height=this.maxHeight,e.width=ti(s)+l),r.display&&this.ticks.length){const{first:u,last:d,widest:h,highest:f}=this._getLabelSizes(),m=r.padding*2,x=At(this.labelRotation),y=Math.cos(x),b=Math.sin(x);if(o){const g=r.mirror?0:b*h.width+y*f.height;e.height=Math.min(this.maxHeight,e.height+g+m)}else{const g=r.mirror?0:y*h.width+b*f.height;e.width=Math.min(this.maxWidth,e.width+g+m)}this._calculatePadding(u,d,b,y)}}this._handleMargins(),o?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,n,r,i){const{ticks:{align:s,padding:a},position:o}=this.options,l=this.labelRotation!==0,u=o!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,m=0;l?u?(f=i*e.width,m=r*n.height):(f=r*e.height,m=i*n.width):s==="start"?m=n.width:s==="end"?f=e.width:s!=="inner"&&(f=e.width/2,m=n.width/2),this.paddingLeft=Math.max((f-d+a)*this.width/(this.width-d),0),this.paddingRight=Math.max((m-h+a)*this.width/(this.width-h),0)}else{let d=n.height/2,h=e.height/2;s==="start"?(d=0,h=e.height):s==="end"&&(d=n.height,h=0),this.paddingTop=d+a,this.paddingBottom=h+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){te(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:n}=this.options;return n==="top"||n==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let n,r;for(n=0,r=e.length;n<r;n++)q(e[n].label)&&(e.splice(n,1),r--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const n=this.options.ticks.sampleSize;let r=this.ticks;n<r.length&&(r=jh(r,n)),this._labelSizes=e=this._computeLabelSizes(r,r.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,n,r){const{ctx:i,_longestTextCache:s}=this,a=[],o=[],l=Math.floor(n/kh(n,r));let u=0,d=0,h,f,m,x,y,b,g,p,v,w,_;for(h=0;h<n;h+=l){if(x=e[h].label,y=this._resolveTickFontOptions(h),i.font=b=y.string,g=s[b]=s[b]||{data:{},gc:[]},p=y.lineHeight,v=w=0,!q(x)&&!ge(x))v=nh(i,g.data,g.gc,v,x),w=p;else if(ge(x))for(f=0,m=x.length;f<m;++f)_=x[f],!q(_)&&!ge(_)&&(v=nh(i,g.data,g.gc,v,_),w+=p);a.push(v),o.push(w),u=Math.max(v,u),d=Math.max(w,d)}f2(s,n);const S=a.indexOf(u),N=o.indexOf(d),j=$=>({width:a[$]||0,height:o[$]||0});return{first:j(0),last:j(n-1),widest:j(S),highest:j(N),widths:a,heights:o}}getLabelForValue(e){return e}getPixelForValue(e,n){return NaN}getValueForPixel(e){}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const n=this._startPixel+e*this._length;return xb(this._alignToPixels?Pn(this.chart,n,0):n)}getDecimalForPixel(e){const n=(e-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:n}=this;return e<0&&n<0?n:e>0&&n>0?e:0}getContext(e){const n=this.ticks||[];if(e>=0&&e<n.length){const r=n[e];return r.$context||(r.$context=p2(this.getContext(),e,r))}return this.$context||(this.$context=m2(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,n=At(this.labelRotation),r=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),s=this._getLabelSizes(),a=e.autoSkipPadding||0,o=s?s.widest.width+a:0,l=s?s.highest.height+a:0;return this.isHorizontal()?l*r>o*i?o/r:l/i:l*i<o*r?l/r:o/i}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const n=this.axis,r=this.chart,i=this.options,{grid:s,position:a,border:o}=i,l=s.offset,u=this.isHorizontal(),h=this.ticks.length+(l?1:0),f=ti(s),m=[],x=o.setContext(this.getContext()),y=x.display?x.width:0,b=y/2,g=function(U){return Pn(r,U,y)};let p,v,w,_,S,N,j,$,M,E,L,R;if(a==="top")p=g(this.bottom),N=this.bottom-f,$=p-b,E=g(e.top)+b,R=e.bottom;else if(a==="bottom")p=g(this.top),E=e.top,R=g(e.bottom)-b,N=p+b,$=this.top+f;else if(a==="left")p=g(this.right),S=this.right-f,j=p-b,M=g(e.left)+b,L=e.right;else if(a==="right")p=g(this.left),M=e.left,L=g(e.right)-b,S=p+b,j=this.left+f;else if(n==="x"){if(a==="center")p=g((e.top+e.bottom)/2+.5);else if(B(a)){const U=Object.keys(a)[0],T=a[U];p=g(this.chart.scales[U].getPixelForValue(T))}E=e.top,R=e.bottom,N=p+b,$=N+f}else if(n==="y"){if(a==="center")p=g((e.left+e.right)/2);else if(B(a)){const U=Object.keys(a)[0],T=a[U];p=g(this.chart.scales[U].getPixelForValue(T))}S=p-b,j=S-f,M=e.left,L=e.right}const V=W(i.ticks.maxTicksLimit,h),I=Math.max(1,Math.ceil(h/V));for(v=0;v<h;v+=I){const U=this.getContext(v),T=s.setContext(U),P=o.setContext(U),O=T.lineWidth,z=T.color,K=P.dash||[],G=P.dashOffset,bt=T.tickWidth,$e=T.tickColor,$t=T.tickBorderDash||[],Ee=T.tickBorderDashOffset;w=h2(this,v,l),w!==void 0&&(_=Pn(r,w,O),u?S=j=M=L=_:N=$=E=R=_,m.push({tx1:S,ty1:N,tx2:j,ty2:$,x1:M,y1:E,x2:L,y2:R,width:O,color:z,borderDash:K,borderDashOffset:G,tickWidth:bt,tickColor:$e,tickBorderDash:$t,tickBorderDashOffset:Ee}))}return this._ticksLength=h,this._borderValue=p,m}_computeLabelItems(e){const n=this.axis,r=this.options,{position:i,ticks:s}=r,a=this.isHorizontal(),o=this.ticks,{align:l,crossAlign:u,padding:d,mirror:h}=s,f=ti(r.grid),m=f+d,x=h?-d:m,y=-At(this.labelRotation),b=[];let g,p,v,w,_,S,N,j,$,M,E,L,R="middle";if(i==="top")S=this.bottom-x,N=this._getXAxisLabelAlignment();else if(i==="bottom")S=this.top+x,N=this._getXAxisLabelAlignment();else if(i==="left"){const I=this._getYAxisLabelAlignment(f);N=I.textAlign,_=I.x}else if(i==="right"){const I=this._getYAxisLabelAlignment(f);N=I.textAlign,_=I.x}else if(n==="x"){if(i==="center")S=(e.top+e.bottom)/2+m;else if(B(i)){const I=Object.keys(i)[0],U=i[I];S=this.chart.scales[I].getPixelForValue(U)+m}N=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")_=(e.left+e.right)/2-m;else if(B(i)){const I=Object.keys(i)[0],U=i[I];_=this.chart.scales[I].getPixelForValue(U)}N=this._getYAxisLabelAlignment(f).textAlign}n==="y"&&(l==="start"?R="top":l==="end"&&(R="bottom"));const V=this._getLabelSizes();for(g=0,p=o.length;g<p;++g){v=o[g],w=v.label;const I=s.setContext(this.getContext(g));j=this.getPixelForTick(g)+s.labelOffset,$=this._resolveTickFontOptions(g),M=$.lineHeight,E=ge(w)?w.length:1;const U=E/2,T=I.color,P=I.textStrokeColor,O=I.textStrokeWidth;let z=N;a?(_=j,N==="inner"&&(g===p-1?z=this.options.reverse?"left":"right":g===0?z=this.options.reverse?"right":"left":z="center"),i==="top"?u==="near"||y!==0?L=-E*M+M/2:u==="center"?L=-V.highest.height/2-U*M+M:L=-V.highest.height+M/2:u==="near"||y!==0?L=M/2:u==="center"?L=V.highest.height/2-U*M:L=V.highest.height-E*M,h&&(L*=-1),y!==0&&!I.showLabelBackdrop&&(_+=M/2*Math.sin(y))):(S=j,L=(1-E)*M/2);let K;if(I.showLabelBackdrop){const G=ct(I.backdropPadding),bt=V.heights[g],$e=V.widths[g];let $t=L-G.top,Ee=0-G.left;switch(R){case"middle":$t-=bt/2;break;case"bottom":$t-=bt;break}switch(N){case"center":Ee-=$e/2;break;case"right":Ee-=$e;break;case"inner":g===p-1?Ee-=$e:g>0&&(Ee-=$e/2);break}K={left:Ee,top:$t,width:$e+G.width,height:bt+G.height,color:I.backdropColor}}b.push({label:w,font:$,textOffset:L,options:{rotation:y,color:T,strokeColor:P,strokeWidth:O,textAlign:z,textBaseline:R,translation:[_,S],backdrop:K}})}return b}_getXAxisLabelAlignment(){const{position:e,ticks:n}=this.options;if(-At(this.labelRotation))return e==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(e){const{position:n,ticks:{crossAlign:r,mirror:i,padding:s}}=this.options,a=this._getLabelSizes(),o=e+s,l=a.widest.width;let u,d;return n==="left"?i?(d=this.right+s,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d+=l)):(d=this.right-o,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d=this.left)):n==="right"?i?(d=this.left+s,r==="near"?u="right":r==="center"?(u="center",d-=l/2):(u="left",d-=l)):(d=this.left+o,r==="near"?u="left":r==="center"?(u="center",d+=l/2):(u="right",d=this.right)):u="right",{textAlign:u,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:n},left:r,top:i,width:s,height:a}=this;n&&(e.save(),e.fillStyle=n,e.fillRect(r,i,s,a),e.restore())}getLineWidthForValue(e){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(s=>s.value===e);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(e){const n=this.options.grid,r=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,a;const o=(l,u,d)=>{!d.width||!d.color||(r.save(),r.lineWidth=d.width,r.strokeStyle=d.color,r.setLineDash(d.borderDash||[]),r.lineDashOffset=d.borderDashOffset,r.beginPath(),r.moveTo(l.x,l.y),r.lineTo(u.x,u.y),r.stroke(),r.restore())};if(n.display)for(s=0,a=i.length;s<a;++s){const l=i[s];n.drawOnChartArea&&o({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&o({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:n,options:{border:r,grid:i}}=this,s=r.setContext(this.getContext()),a=r.display?s.width:0;if(!a)return;const o=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let u,d,h,f;this.isHorizontal()?(u=Pn(e,this.left,a)-a/2,d=Pn(e,this.right,o)+o/2,h=f=l):(h=Pn(e,this.top,a)-a/2,f=Pn(e,this.bottom,o)+o/2,u=d=l),n.save(),n.lineWidth=s.width,n.strokeStyle=s.color,n.beginPath(),n.moveTo(u,h),n.lineTo(d,f),n.stroke(),n.restore()}drawLabels(e){if(!this.options.ticks.display)return;const r=this.ctx,i=this._computeLabelArea();i&&ou(r,i);const s=this.getLabelItems(e);for(const a of s){const o=a.options,l=a.font,u=a.label,d=a.textOffset;Xi(r,u,0,d,l,o)}i&&lu(r)}drawTitle(){const{ctx:e,options:{position:n,title:r,reverse:i}}=this;if(!r.display)return;const s=Pe(r.font),a=ct(r.padding),o=r.align;let l=s.lineHeight/2;n==="bottom"||n==="center"||B(n)?(l+=a.bottom,ge(r.text)&&(l+=s.lineHeight*(r.text.length-1))):l+=a.top;const{titleX:u,titleY:d,maxWidth:h,rotation:f}=x2(this,l,n,o);Xi(e,r.text,0,0,s,{color:r.color,maxWidth:h,rotation:f,textAlign:g2(o,n,i),textBaseline:"middle",translation:[u,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,n=e.ticks&&e.ticks.z||0,r=W(e.grid&&e.grid.z,-1),i=W(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Ur.prototype.draw?[{z:n,draw:s=>{this.draw(s)}}]:[{z:r,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const n=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",i=[];let s,a;for(s=0,a=n.length;s<a;++s){const o=n[s];o[r]===this.id&&(!e||o.type===e)&&i.push(o)}return i}_resolveTickFontOptions(e){const n=this.options.ticks.setContext(this.getContext(e));return Pe(n.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Es{constructor(e,n,r){this.type=e,this.scope=n,this.override=r,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const n=Object.getPrototypeOf(e);let r;b2(n)&&(r=this.register(n));const i=this.items,s=e.id,a=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in i||(i[s]=e,y2(e,a,r),this.override&&ue.override(e.id,e.overrides)),a}get(e){return this.items[e]}unregister(e){const n=this.items,r=e.id,i=this.scope;r in n&&delete n[r],i&&r in ue[i]&&(delete ue[i][r],this.override&&delete Kn[r])}}function y2(t,e,n){const r=Vi(Object.create(null),[n?ue.get(n):{},ue.get(e),t.defaults]);ue.set(e,r),t.defaultRoutes&&v2(e,t.defaultRoutes),t.descriptors&&ue.describe(e,t.descriptors)}function v2(t,e){Object.keys(e).forEach(n=>{const r=n.split("."),i=r.pop(),s=[t].concat(r).join("."),a=e[n].split("."),o=a.pop(),l=a.join(".");ue.route(s,i,l,o)})}function b2(t){return"id"in t&&"defaults"in t}class w2{constructor(){this.controllers=new Es(Nr,"datasets",!0),this.elements=new Es(Vt,"elements"),this.plugins=new Es(Object,"plugins"),this.scales=new Es(Ur,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,n,r){[...n].forEach(i=>{const s=r||this._getRegistryForType(i);r||s.isForType(i)||s===this.plugins&&i.id?this._exec(e,s,i):X(i,a=>{const o=r||this._getRegistryForType(a);this._exec(e,o,a)})})}_exec(e,n,r){const i=ru(e);te(r["before"+i],[],r),n[e](r),te(r["after"+i],[],r)}_getRegistryForType(e){for(let n=0;n<this._typedRegistries.length;n++){const r=this._typedRegistries[n];if(r.isForType(e))return r}return this.plugins}_get(e,n,r){const i=n.get(e);if(i===void 0)throw new Error('"'+e+'" is not a registered '+r+".");return i}}var kt=new w2;class S2{constructor(){this._init=[]}notify(e,n,r,i){n==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=i?this._descriptors(e).filter(i):this._descriptors(e),a=this._notify(s,e,n,r);return n==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),a}_notify(e,n,r,i){i=i||{};for(const s of e){const a=s.plugin,o=a[r],l=[n,i,s.options];if(te(o,l,a)===!1&&i.cancelable)return!1}return!0}invalidate(){q(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),n}_createDescriptors(e,n){const r=e&&e.config,i=W(r.options&&r.options.plugins,{}),s=_2(r);return i===!1&&!n?[]:k2(e,s,i,n)}_notifyStateChanges(e){const n=this._oldCache||[],r=this._cache,i=(s,a)=>s.filter(o=>!a.some(l=>o.plugin.id===l.plugin.id));this._notify(i(n,r),e,"stop"),this._notify(i(r,n),e,"start")}}function _2(t){const e={},n=[],r=Object.keys(kt.plugins.items);for(let s=0;s<r.length;s++)n.push(kt.getPlugin(r[s]));const i=t.plugins||[];for(let s=0;s<i.length;s++){const a=i[s];n.indexOf(a)===-1&&(n.push(a),e[a.id]=!0)}return{plugins:n,localIds:e}}function N2(t,e){return!e&&t===!1?null:t===!0?{}:t}function k2(t,{plugins:e,localIds:n},r,i){const s=[],a=t.getContext();for(const o of e){const l=o.id,u=N2(r[l],i);u!==null&&s.push({plugin:o,options:j2(t.config,{plugin:o,local:n[l]},u,a)})}return s}function j2(t,{plugin:e,local:n},r,i){const s=t.pluginScopeKeys(e),a=t.getOptionScopes(r,s);return n&&e.defaults&&a.push(e.defaults),t.createResolver(a,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Jl(t,e){const n=ue.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||n.indexAxis||"x"}function C2(t,e){let n=t;return t==="_index_"?n=e:t==="_value_"&&(n=e==="x"?"y":"x"),n}function P2(t,e){return t===e?"_index_":"_value_"}function Ph(t){if(t==="x"||t==="y"||t==="r")return t}function M2(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function ec(t,...e){if(Ph(t))return t;for(const n of e){const r=n.axis||M2(n.position)||t.length>1&&Ph(t[0].toLowerCase());if(r)return r}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Mh(t,e,n){if(n[e+"AxisID"]===t)return{axis:e}}function $2(t,e){if(e.data&&e.data.datasets){const n=e.data.datasets.filter(r=>r.xAxisID===t||r.yAxisID===t);if(n.length)return Mh(t,"x",n[0])||Mh(t,"y",n[0])}return{}}function E2(t,e){const n=Kn[t.type]||{scales:{}},r=e.scales||{},i=Jl(t.type,e),s=Object.create(null);return Object.keys(r).forEach(a=>{const o=r[a];if(!B(o))return console.error(`Invalid scale configuration for scale: ${a}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=ec(a,o,$2(a,t),ue.scales[o.type]),u=P2(l,i),d=n.scales||{};s[a]=wi(Object.create(null),[{axis:l},o,d[l],d[u]])}),t.data.datasets.forEach(a=>{const o=a.type||t.type,l=a.indexAxis||Jl(o,e),d=(Kn[o]||{}).scales||{};Object.keys(d).forEach(h=>{const f=C2(h,l),m=a[f+"AxisID"]||f;s[m]=s[m]||Object.create(null),wi(s[m],[{axis:f},r[m],d[h]])})}),Object.keys(s).forEach(a=>{const o=s[a];wi(o,[ue.scales[o.type],ue.scale])}),s}function mg(t){const e=t.options||(t.options={});e.plugins=W(e.plugins,{}),e.scales=E2(t,e)}function pg(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function T2(t){return t=t||{},t.data=pg(t.data),mg(t),t}const $h=new Map,gg=new Set;function Ts(t,e){let n=$h.get(t);return n||(n=e(),$h.set(t,n),gg.add(n)),n}const ni=(t,e,n)=>{const r=Qn(e,n);r!==void 0&&t.add(r)};class R2{constructor(e){this._config=T2(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=pg(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),mg(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Ts(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,n){return Ts(`${e}.transition.${n}`,()=>[[`datasets.${e}.transitions.${n}`,`transitions.${n}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,n){return Ts(`${e}-${n}`,()=>[[`datasets.${e}.elements.${n}`,`datasets.${e}`,`elements.${n}`,""]])}pluginScopeKeys(e){const n=e.id,r=this.type;return Ts(`${r}-plugin-${n}`,()=>[[`plugins.${n}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,n){const r=this._scopeCache;let i=r.get(e);return(!i||n)&&(i=new Map,r.set(e,i)),i}getOptionScopes(e,n,r){const{options:i,type:s}=this,a=this._cachedScopes(e,r),o=a.get(n);if(o)return o;const l=new Set;n.forEach(d=>{e&&(l.add(e),d.forEach(h=>ni(l,e,h))),d.forEach(h=>ni(l,i,h)),d.forEach(h=>ni(l,Kn[s]||{},h)),d.forEach(h=>ni(l,ue,h)),d.forEach(h=>ni(l,Zl,h))});const u=Array.from(l);return u.length===0&&u.push(Object.create(null)),gg.has(n)&&a.set(n,u),u}chartOptionScopes(){const{options:e,type:n}=this;return[e,Kn[n]||{},ue.datasets[n]||{},{type:n},ue,Zl]}resolveNamedOptions(e,n,r,i=[""]){const s={$shared:!0},{resolver:a,subPrefixes:o}=Eh(this._resolverCache,e,i);let l=a;if(O2(a,n)){s.$shared=!1,r=bn(r)?r():r;const u=this.createResolver(e,r,o);l=Rr(a,r,u)}for(const u of n)s[u]=l[u];return s}createResolver(e,n,r=[""],i){const{resolver:s}=Eh(this._resolverCache,e,r);return B(n)?Rr(s,n,void 0,i):s}}function Eh(t,e,n){let r=t.get(e);r||(r=new Map,t.set(e,r));const i=n.join();let s=r.get(i);return s||(s={resolver:uu(e,n),subPrefixes:n.filter(o=>!o.toLowerCase().includes("hover"))},r.set(i,s)),s}const D2=t=>B(t)&&Object.getOwnPropertyNames(t).some(e=>bn(t[e]));function O2(t,e){const{isScriptable:n,isIndexable:r}=eg(t);for(const i of e){const s=n(i),a=r(i),o=(a||s)&&t[i];if(s&&(bn(o)||D2(o))||a&&ge(o))return!0}return!1}var L2="4.4.9";const z2=["top","bottom","left","right","chartArea"];function Th(t,e){return t==="top"||t==="bottom"||z2.indexOf(t)===-1&&e==="x"}function Rh(t,e){return function(n,r){return n[t]===r[t]?n[e]-r[e]:n[t]-r[t]}}function Dh(t){const e=t.chart,n=e.options.animation;e.notifyPlugins("afterRender"),te(n&&n.onComplete,[t],e)}function F2(t){const e=t.chart,n=e.options.animation;te(n&&n.onProgress,[t],e)}function xg(t){return fu()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const Gs={},Oh=t=>{const e=xg(t);return Object.values(Gs).filter(n=>n.canvas===e).pop()};function A2(t,e,n){const r=Object.keys(t);for(const i of r){const s=+i;if(s>=e){const a=t[i];delete t[i],(n>0||s>e)&&(t[s+n]=a)}}}function I2(t,e,n,r){return!n||t.type==="mouseout"?null:r?e:t}var Kt;let ns=(Kt=class{static register(...e){kt.add(...e),Lh()}static unregister(...e){kt.remove(...e),Lh()}constructor(e,n){const r=this.config=new R2(n),i=xg(e),s=Oh(i);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const a=r.createResolver(r.chartOptionScopes(),this.getContext());this.platform=new(r.platform||i2(i)),this.platform.updateConfig(r);const o=this.platform.acquireContext(i,a.aspectRatio),l=o&&o.canvas,u=l&&l.height,d=l&&l.width;if(this.id=rb(),this.ctx=o,this.canvas=l,this.width=d,this.height=u,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new S2,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=wb(h=>this.update(h),a.resizeDelay||0),this._dataChanges=[],Gs[this.id]=this,!o||!l){console.error("Failed to create chart: can't acquire context from the given item");return}Tt.listen(this,"complete",Dh),Tt.listen(this,"progress",F2),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:n},width:r,height:i,_aspectRatio:s}=this;return q(e)?n&&s?s:i?r/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return kt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():oh(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return rh(this.canvas,this.ctx),this}stop(){return Tt.stop(this),this}resize(e,n){Tt.running(this)?this._resizeBeforeDraw={width:e,height:n}:this._resize(e,n)}_resize(e,n){const r=this.options,i=this.canvas,s=r.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(i,e,n,s),o=r.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,oh(this,o,!0)&&(this.notifyPlugins("resize",{size:a}),te(r.onResize,[this,a],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};X(n,(r,i)=>{r.id=i})}buildOrUpdateScales(){const e=this.options,n=e.scales,r=this.scales,i=Object.keys(r).reduce((a,o)=>(a[o]=!1,a),{});let s=[];n&&(s=s.concat(Object.keys(n).map(a=>{const o=n[a],l=ec(a,o),u=l==="r",d=l==="x";return{options:o,dposition:u?"chartArea":d?"bottom":"left",dtype:u?"radialLinear":d?"category":"linear"}}))),X(s,a=>{const o=a.options,l=o.id,u=ec(l,o),d=W(o.type,a.dtype);(o.position===void 0||Th(o.position,u)!==Th(a.dposition))&&(o.position=a.dposition),i[l]=!0;let h=null;if(l in r&&r[l].type===d)h=r[l];else{const f=kt.getScale(d);h=new f({id:l,type:d,ctx:this.ctx,chart:this}),r[h.id]=h}h.init(o,e)}),X(i,(a,o)=>{a||delete r[o]}),X(r,a=>{it.configure(this,a,a.options),it.addBox(this,a)})}_updateMetasets(){const e=this._metasets,n=this.data.datasets.length,r=e.length;if(e.sort((i,s)=>i.index-s.index),r>n){for(let i=n;i<r;++i)this._destroyDatasetMeta(i);e.splice(n,r-n)}this._sortedMetasets=e.slice(0).sort(Rh("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:n}}=this;e.length>n.length&&delete this._stacks,e.forEach((r,i)=>{n.filter(s=>s===r._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const e=[],n=this.data.datasets;let r,i;for(this._removeUnreferencedMetasets(),r=0,i=n.length;r<i;r++){const s=n[r];let a=this.getDatasetMeta(r);const o=s.type||this.config.type;if(a.type&&a.type!==o&&(this._destroyDatasetMeta(r),a=this.getDatasetMeta(r)),a.type=o,a.indexAxis=s.indexAxis||Jl(o,this.options),a.order=s.order||0,a.index=r,a.label=""+s.label,a.visible=this.isDatasetVisible(r),a.controller)a.controller.updateIndex(r),a.controller.linkScales();else{const l=kt.getController(o),{datasetElementType:u,dataElementType:d}=ue.datasets[o];Object.assign(l,{dataElementType:kt.getElement(d),datasetElementType:u&&kt.getElement(u)}),a.controller=new l(this,r),e.push(a.controller)}}return this._updateMetasets(),e}_resetElements(){X(this.data.datasets,(e,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const n=this.config;n.update();const r=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!r.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let u=0,d=this.data.datasets.length;u<d;u++){const{controller:h}=this.getDatasetMeta(u),f=!i&&s.indexOf(h)===-1;h.buildOrUpdateElements(f),a=Math.max(+h.getMaxOverflow(),a)}a=this._minPadding=r.layout.autoPadding?a:0,this._updateLayout(a),i||X(s,u=>{u.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Rh("z","_idx"));const{_active:o,_lastEvent:l}=this;l?this._eventHandler(l,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){X(this.scales,e=>{it.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,n=new Set(Object.keys(this._listeners)),r=new Set(e.events);(!Xd(n,r)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,n=this._getUniformDataChanges()||[];for(const{method:r,start:i,count:s}of n){const a=r==="_removeElements"?-s:s;A2(e,i,a)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const n=this.data.datasets.length,r=s=>new Set(e.filter(a=>a[0]===s).map((a,o)=>o+","+a.splice(1).join(","))),i=r(0);for(let s=1;s<n;s++)if(!Xd(i,r(s)))return;return Array.from(i).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;it.update(this,this.width,this.height,e);const n=this.chartArea,r=n.width<=0||n.height<=0;this._layers=[],X(this.boxes,i=>{r&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,s)=>{i._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let n=0,r=this.data.datasets.length;n<r;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,r=this.data.datasets.length;n<r;++n)this._updateDataset(n,bn(e)?e({datasetIndex:n}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,n){const r=this.getDatasetMeta(e),i={meta:r,index:e,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(r.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(Tt.has(this)?this.attached&&!Tt.running(this)&&Tt.start(this):(this.draw(),Dh({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:r,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(r,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(e=0;e<n.length&&n[e].z<=0;++e)n[e].draw(this.chartArea);for(this._drawDatasets();e<n.length;++e)n[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const n=this._sortedMetasets,r=[];let i,s;for(i=0,s=n.length;i<s;++i){const a=n[i];(!e||a.visible)&&r.push(a)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let n=e.length-1;n>=0;--n)this._drawDataset(e[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const n=this.ctx,r={meta:e,index:e.index,cancelable:!0},i=aw(this,e);this.notifyPlugins("beforeDatasetDraw",r)!==!1&&(i&&ou(n,i),e.controller.draw(),i&&lu(n),r.cancelable=!1,this.notifyPlugins("afterDatasetDraw",r))}isPointInArea(e){return Zp(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,n,r,i){const s=zw.modes[n];return typeof s=="function"?s(this,e,r,i):[]}getDatasetMeta(e){const n=this.data.datasets[e],r=this._metasets;let i=r.filter(s=>s&&s._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:e,_dataset:n,_parsed:[],_sorted:!1},r.push(i)),i}getContext(){return this.$context||(this.$context=Ir(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const n=this.data.datasets[e];if(!n)return!1;const r=this.getDatasetMeta(e);return typeof r.hidden=="boolean"?!r.hidden:!n.hidden}setDatasetVisibility(e,n){const r=this.getDatasetMeta(e);r.hidden=!n}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,n,r){const i=r?"show":"hide",s=this.getDatasetMeta(e),a=s.controller._resolveAnimations(void 0,i);Yi(n)?(s.data[n].hidden=!r,this.update()):(this.setDatasetVisibility(e,r),a.update(s,{visible:r}),this.update(o=>o.datasetIndex===e?i:void 0))}hide(e,n){this._updateVisibility(e,n,!1)}show(e,n){this._updateVisibility(e,n,!0)}_destroyDatasetMeta(e){const n=this._metasets[e];n&&n.controller&&n.controller._destroy(),delete this._metasets[e]}_stop(){let e,n;for(this.stop(),Tt.remove(this),e=0,n=this.data.datasets.length;e<n;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:n}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),rh(e,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete Gs[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,n=this.platform,r=(s,a)=>{n.addEventListener(this,s,a),e[s]=a},i=(s,a,o)=>{s.offsetX=a,s.offsetY=o,this._eventHandler(s)};X(this.options.events,s=>r(s,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,n=this.platform,r=(l,u)=>{n.addEventListener(this,l,u),e[l]=u},i=(l,u)=>{e[l]&&(n.removeEventListener(this,l,u),delete e[l])},s=(l,u)=>{this.canvas&&this.resize(l,u)};let a;const o=()=>{i("attach",o),this.attached=!0,this.resize(),r("resize",s),r("detach",a)};a=()=>{this.attached=!1,i("resize",s),this._stop(),this._resize(0,0),r("attach",o)},n.isAttached(this.canvas)?o():a()}unbindEvents(){X(this._listeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._listeners={},X(this._responsiveListeners,(e,n)=>{this.platform.removeEventListener(this,n,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,n,r){const i=r?"set":"remove";let s,a,o,l;for(n==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+i+"DatasetHoverStyle"]()),o=0,l=e.length;o<l;++o){a=e[o];const u=a&&this.getDatasetMeta(a.datasetIndex).controller;u&&u[i+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const n=this._active||[],r=e.map(({datasetIndex:s,index:a})=>{const o=this.getDatasetMeta(s);if(!o)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:o.data[a],index:a}});!Ma(r,n)&&(this._active=r,this._lastEvent=null,this._updateHoverStyles(r,n))}notifyPlugins(e,n,r){return this._plugins.notify(this,e,n,r)}isPluginEnabled(e){return this._plugins._cache.filter(n=>n.plugin.id===e).length===1}_updateHoverStyles(e,n,r){const i=this.options.hover,s=(l,u)=>l.filter(d=>!u.some(h=>d.datasetIndex===h.datasetIndex&&d.index===h.index)),a=s(n,e),o=r?e:s(e,n);a.length&&this.updateHoverStyle(a,i.mode,!1),o.length&&i.mode&&this.updateHoverStyle(o,i.mode,!0)}_eventHandler(e,n){const r={event:e,replay:n,cancelable:!0,inChartArea:this.isPointInArea(e)},i=a=>(a.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",r,i)===!1)return;const s=this._handleEvent(e,n,r.inChartArea);return r.cancelable=!1,this.notifyPlugins("afterEvent",r,i),(s||r.changed)&&this.render(),this}_handleEvent(e,n,r){const{_active:i=[],options:s}=this,a=n,o=this._getActiveElements(e,i,r,a),l=cb(e),u=I2(e,this._lastEvent,r,l);r&&(this._lastEvent=null,te(s.onHover,[e,o,this],this),l&&te(s.onClick,[e,o,this],this));const d=!Ma(o,i);return(d||n)&&(this._active=o,this._updateHoverStyles(o,i,n)),this._lastEvent=u,d}_getActiveElements(e,n,r,i){if(e.type==="mouseout")return[];if(!r)return n;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,i)}},F(Kt,"defaults",ue),F(Kt,"instances",Gs),F(Kt,"overrides",Kn),F(Kt,"registry",kt),F(Kt,"version",L2),F(Kt,"getChart",Oh),Kt);function Lh(){return X(ns.instances,t=>t._plugins.invalidate())}function U2(t,e,n){const{startAngle:r,pixelMargin:i,x:s,y:a,outerRadius:o,innerRadius:l}=e;let u=i/o;t.beginPath(),t.arc(s,a,o,r-u,n+u),l>i?(u=i/l,t.arc(s,a,l,n+u,r-u,!0)):t.arc(s,a,i,n+ye,r-ye),t.closePath(),t.clip()}function H2(t){return cu(t,["outerStart","outerEnd","innerStart","innerEnd"])}function B2(t,e,n,r){const i=H2(t.options.borderRadius),s=(n-e)/2,a=Math.min(s,r*e/2),o=l=>{const u=(n-Math.min(s,l))*r/2;return He(l,0,Math.min(s,u))};return{outerStart:o(i.outerStart),outerEnd:o(i.outerEnd),innerStart:He(i.innerStart,0,a),innerEnd:He(i.innerEnd,0,a)}}function nr(t,e,n,r){return{x:n+t*Math.cos(e),y:r+t*Math.sin(e)}}function za(t,e,n,r,i,s){const{x:a,y:o,startAngle:l,pixelMargin:u,innerRadius:d}=e,h=Math.max(e.outerRadius+r+n-u,0),f=d>0?d+r+n+u:0;let m=0;const x=i-l;if(r){const I=d>0?d-r:0,U=h>0?h-r:0,T=(I+U)/2,P=T!==0?x*T/(T+r):x;m=(x-P)/2}const y=Math.max(.001,x*h-n/fe)/h,b=(x-y)/2,g=l+b+m,p=i-b-m,{outerStart:v,outerEnd:w,innerStart:_,innerEnd:S}=B2(e,f,h,p-g),N=h-v,j=h-w,$=g+v/N,M=p-w/j,E=f+_,L=f+S,R=g+_/E,V=p-S/L;if(t.beginPath(),s){const I=($+M)/2;if(t.arc(a,o,h,$,I),t.arc(a,o,h,I,M),w>0){const O=nr(j,M,a,o);t.arc(O.x,O.y,w,M,p+ye)}const U=nr(L,p,a,o);if(t.lineTo(U.x,U.y),S>0){const O=nr(L,V,a,o);t.arc(O.x,O.y,S,p+ye,V+Math.PI)}const T=(p-S/f+(g+_/f))/2;if(t.arc(a,o,f,p-S/f,T,!0),t.arc(a,o,f,T,g+_/f,!0),_>0){const O=nr(E,R,a,o);t.arc(O.x,O.y,_,R+Math.PI,g-ye)}const P=nr(N,g,a,o);if(t.lineTo(P.x,P.y),v>0){const O=nr(N,$,a,o);t.arc(O.x,O.y,v,g-ye,$)}}else{t.moveTo(a,o);const I=Math.cos($)*h+a,U=Math.sin($)*h+o;t.lineTo(I,U);const T=Math.cos(M)*h+a,P=Math.sin(M)*h+o;t.lineTo(T,P)}t.closePath()}function W2(t,e,n,r,i){const{fullCircles:s,startAngle:a,circumference:o}=e;let l=e.endAngle;if(s){za(t,e,n,r,l,i);for(let u=0;u<s;++u)t.fill();isNaN(o)||(l=a+(o%he||he))}return za(t,e,n,r,l,i),t.fill(),l}function V2(t,e,n,r,i){const{fullCircles:s,startAngle:a,circumference:o,options:l}=e,{borderWidth:u,borderJoinStyle:d,borderDash:h,borderDashOffset:f}=l,m=l.borderAlign==="inner";if(!u)return;t.setLineDash(h||[]),t.lineDashOffset=f,m?(t.lineWidth=u*2,t.lineJoin=d||"round"):(t.lineWidth=u,t.lineJoin=d||"bevel");let x=e.endAngle;if(s){za(t,e,n,r,x,i);for(let y=0;y<s;++y)t.stroke();isNaN(o)||(x=a+(o%he||he))}m&&U2(t,e,x),s||(za(t,e,n,r,x,i),t.stroke())}class ui extends Vt{constructor(n){super();F(this,"circumference");F(this,"endAngle");F(this,"fullCircles");F(this,"innerRadius");F(this,"outerRadius");F(this,"pixelMargin");F(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,r,i){const s=this.getProps(["x","y"],i),{angle:a,distance:o}=Wp(s,{x:n,y:r}),{startAngle:l,endAngle:u,innerRadius:d,outerRadius:h,circumference:f}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),m=(this.options.spacing+this.options.borderWidth)/2,x=W(f,u-l),y=Ra(a,l,u)&&l!==u,b=x>=he||y,g=Fn(o,d+m,h+m);return b&&g}getCenterPoint(n){const{x:r,y:i,startAngle:s,endAngle:a,innerRadius:o,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:u,spacing:d}=this.options,h=(s+a)/2,f=(o+l+d+u)/2;return{x:r+Math.cos(h)*f,y:i+Math.sin(h)*f}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:r,circumference:i}=this,s=(r.offset||0)/4,a=(r.spacing||0)/2,o=r.circular;if(this.pixelMargin=r.borderAlign==="inner"?.33:0,this.fullCircles=i>he?Math.floor(i/he):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*s,Math.sin(l)*s);const u=1-Math.sin(Math.min(fe,i||0)),d=s*u;n.fillStyle=r.backgroundColor,n.strokeStyle=r.borderColor,W2(n,this,d,a,o),V2(n,this,d,a,o),n.restore()}}F(ui,"id","arc"),F(ui,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),F(ui,"defaultRoutes",{backgroundColor:"backgroundColor"}),F(ui,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function yg(t,e){const{x:n,y:r,base:i,width:s,height:a}=t.getProps(["x","y","base","width","height"],e);let o,l,u,d,h;return t.horizontal?(h=a/2,o=Math.min(n,i),l=Math.max(n,i),u=r-h,d=r+h):(h=s/2,o=n-h,l=n+h,u=Math.min(r,i),d=Math.max(r,i)),{left:o,top:u,right:l,bottom:d}}function sn(t,e,n,r){return t?0:He(e,n,r)}function Y2(t,e,n){const r=t.options.borderWidth,i=t.borderSkipped,s=Jp(r);return{t:sn(i.top,s.top,0,n),r:sn(i.right,s.right,0,e),b:sn(i.bottom,s.bottom,0,n),l:sn(i.left,s.left,0,e)}}function X2(t,e,n){const{enableBorderRadius:r}=t.getProps(["enableBorderRadius"]),i=t.options.borderRadius,s=Sr(i),a=Math.min(e,n),o=t.borderSkipped,l=r||B(i);return{topLeft:sn(!l||o.top||o.left,s.topLeft,0,a),topRight:sn(!l||o.top||o.right,s.topRight,0,a),bottomLeft:sn(!l||o.bottom||o.left,s.bottomLeft,0,a),bottomRight:sn(!l||o.bottom||o.right,s.bottomRight,0,a)}}function Q2(t){const e=yg(t),n=e.right-e.left,r=e.bottom-e.top,i=Y2(t,n/2,r/2),s=X2(t,n/2,r/2);return{outer:{x:e.left,y:e.top,w:n,h:r,radius:s},inner:{x:e.left+i.l,y:e.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,s.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(i.b,i.r))}}}}function Qo(t,e,n,r){const i=e===null,s=n===null,o=t&&!(i&&s)&&yg(t,r);return o&&(i||Fn(e,o.left,o.right))&&(s||Fn(n,o.top,o.bottom))}function K2(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function G2(t,e){t.rect(e.x,e.y,e.w,e.h)}function Ko(t,e,n={}){const r=t.x!==n.x?-e:0,i=t.y!==n.y?-e:0,s=(t.x+t.w!==n.x+n.w?e:0)-r,a=(t.y+t.h!==n.y+n.h?e:0)-i;return{x:t.x+r,y:t.y+i,w:t.w+s,h:t.h+a,radius:t.radius}}class qs extends Vt{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:n,options:{borderColor:r,backgroundColor:i}}=this,{inner:s,outer:a}=Q2(this),o=K2(a.radius)?Da:G2;e.save(),(a.w!==s.w||a.h!==s.h)&&(e.beginPath(),o(e,Ko(a,n,s)),e.clip(),o(e,Ko(s,-n,a)),e.fillStyle=r,e.fill("evenodd")),e.beginPath(),o(e,Ko(s,n)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,n,r){return Qo(this,e,n,r)}inXRange(e,n){return Qo(this,e,null,n)}inYRange(e,n){return Qo(this,null,e,n)}getCenterPoint(e){const{x:n,y:r,base:i,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(n+i)/2:n,y:s?r:(r+i)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}F(qs,"id","bar"),F(qs,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),F(qs,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const zh=(t,e)=>{let{boxHeight:n=e,boxWidth:r=e}=t;return t.usePointStyle&&(n=Math.min(n,e),r=t.pointStyleWidth||Math.min(r,e)),{boxWidth:r,boxHeight:n,itemHeight:Math.max(e,n)}},q2=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Fh extends Vt{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n,r){this.maxWidth=e,this.maxHeight=n,this._margins=r,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let n=te(e.generateLabels,[this.chart],this)||[];e.filter&&(n=n.filter(r=>e.filter(r,this.chart.data))),e.sort&&(n=n.sort((r,i)=>e.sort(r,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:e,ctx:n}=this;if(!e.display){this.width=this.height=0;return}const r=e.labels,i=Pe(r.font),s=i.size,a=this._computeTitleHeight(),{boxWidth:o,itemHeight:l}=zh(r,s);let u,d;n.font=i.string,this.isHorizontal()?(u=this.maxWidth,d=this._fitRows(a,s,o,l)+10):(d=this.maxHeight,u=this._fitCols(a,i,o,l)+10),this.width=Math.min(u,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,n,r,i){const{ctx:s,maxWidth:a,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],u=this.lineWidths=[0],d=i+o;let h=e;s.textAlign="left",s.textBaseline="middle";let f=-1,m=-d;return this.legendItems.forEach((x,y)=>{const b=r+n/2+s.measureText(x.text).width;(y===0||u[u.length-1]+b+2*o>a)&&(h+=d,u[u.length-(y>0?0:1)]=0,m+=d,f++),l[y]={left:0,top:m,row:f,width:b,height:i},u[u.length-1]+=b+o}),h}_fitCols(e,n,r,i){const{ctx:s,maxHeight:a,options:{labels:{padding:o}}}=this,l=this.legendHitBoxes=[],u=this.columnSizes=[],d=a-e;let h=o,f=0,m=0,x=0,y=0;return this.legendItems.forEach((b,g)=>{const{itemWidth:p,itemHeight:v}=Z2(r,n,s,b,i);g>0&&m+v+2*o>d&&(h+=f+o,u.push({width:f,height:m}),x+=f+o,y++,f=m=0),l[g]={left:x,top:m,col:y,width:p,height:v},f=Math.max(f,p),m+=v+o}),h+=f,u.push({width:f,height:m}),h}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:r,labels:{padding:i},rtl:s}}=this,a=_r(s,this.left,this.width);if(this.isHorizontal()){let o=0,l=je(r,this.left+i,this.right-this.lineWidths[o]);for(const u of n)o!==u.row&&(o=u.row,l=je(r,this.left+i,this.right-this.lineWidths[o])),u.top+=this.top+e+i,u.left=a.leftForLtr(a.x(l),u.width),l+=u.width+i}else{let o=0,l=je(r,this.top+e+i,this.bottom-this.columnSizes[o].height);for(const u of n)u.col!==o&&(o=u.col,l=je(r,this.top+e+i,this.bottom-this.columnSizes[o].height)),u.top=l,u.left+=this.left+i,u.left=a.leftForLtr(a.x(u.left),u.width),l+=u.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;ou(e,this),this._draw(),lu(e)}}_draw(){const{options:e,columnSizes:n,lineWidths:r,ctx:i}=this,{align:s,labels:a}=e,o=ue.color,l=_r(e.rtl,this.left,this.width),u=Pe(a.font),{padding:d}=a,h=u.size,f=h/2;let m;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=u.string;const{boxWidth:x,boxHeight:y,itemHeight:b}=zh(a,h),g=function(S,N,j){if(isNaN(x)||x<=0||isNaN(y)||y<0)return;i.save();const $=W(j.lineWidth,1);if(i.fillStyle=W(j.fillStyle,o),i.lineCap=W(j.lineCap,"butt"),i.lineDashOffset=W(j.lineDashOffset,0),i.lineJoin=W(j.lineJoin,"miter"),i.lineWidth=$,i.strokeStyle=W(j.strokeStyle,o),i.setLineDash(W(j.lineDash,[])),a.usePointStyle){const M={radius:y*Math.SQRT2/2,pointStyle:j.pointStyle,rotation:j.rotation,borderWidth:$},E=l.xPlus(S,x/2),L=N+f;qp(i,M,E,L,a.pointStyleWidth&&x)}else{const M=N+Math.max((h-y)/2,0),E=l.leftForLtr(S,x),L=Sr(j.borderRadius);i.beginPath(),Object.values(L).some(R=>R!==0)?Da(i,{x:E,y:M,w:x,h:y,radius:L}):i.rect(E,M,x,y),i.fill(),$!==0&&i.stroke()}i.restore()},p=function(S,N,j){Xi(i,j.text,S,N+b/2,u,{strikethrough:j.hidden,textAlign:l.textAlign(j.textAlign)})},v=this.isHorizontal(),w=this._computeTitleHeight();v?m={x:je(s,this.left+d,this.right-r[0]),y:this.top+d+w,line:0}:m={x:this.left+d,y:je(s,this.top+w+d,this.bottom-n[0].height),line:0},ig(this.ctx,e.textDirection);const _=b+d;this.legendItems.forEach((S,N)=>{i.strokeStyle=S.fontColor,i.fillStyle=S.fontColor;const j=i.measureText(S.text).width,$=l.textAlign(S.textAlign||(S.textAlign=a.textAlign)),M=x+f+j;let E=m.x,L=m.y;l.setWidth(this.width),v?N>0&&E+M+d>this.right&&(L=m.y+=_,m.line++,E=m.x=je(s,this.left+d,this.right-r[m.line])):N>0&&L+_>this.bottom&&(E=m.x=E+n[m.line].width+d,m.line++,L=m.y=je(s,this.top+w+d,this.bottom-n[m.line].height));const R=l.x(E);if(g(R,L,S),E=Sb($,E+x+f,v?E+M:this.right,e.rtl),p(l.x(E),L,S),v)m.x+=M+d;else if(typeof S.text!="string"){const V=u.lineHeight;m.y+=vg(S,V)+d}else m.y+=_}),sg(this.ctx,e.textDirection)}drawTitle(){const e=this.options,n=e.title,r=Pe(n.font),i=ct(n.padding);if(!n.display)return;const s=_r(e.rtl,this.left,this.width),a=this.ctx,o=n.position,l=r.size/2,u=i.top+l;let d,h=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),d=this.top+u,h=je(e.align,h,this.right-f);else{const x=this.columnSizes.reduce((y,b)=>Math.max(y,b.height),0);d=u+je(e.align,this.top,this.bottom-x-e.labels.padding-this._computeTitleHeight())}const m=je(o,h,h+f);a.textAlign=s.textAlign(su(o)),a.textBaseline="middle",a.strokeStyle=n.color,a.fillStyle=n.color,a.font=r.string,Xi(a,n.text,m,d,r)}_computeTitleHeight(){const e=this.options.title,n=Pe(e.font),r=ct(e.padding);return e.display?n.lineHeight+r.height:0}_getLegendItemAt(e,n){let r,i,s;if(Fn(e,this.left,this.right)&&Fn(n,this.top,this.bottom)){for(s=this.legendHitBoxes,r=0;r<s.length;++r)if(i=s[r],Fn(e,i.left,i.left+i.width)&&Fn(n,i.top,i.top+i.height))return this.legendItems[r]}return null}handleEvent(e){const n=this.options;if(!tS(e.type,n))return;const r=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const i=this._hoveredItem,s=q2(i,r);i&&!s&&te(n.onLeave,[e,i,this],this),this._hoveredItem=r,r&&!s&&te(n.onHover,[e,r,this],this)}else r&&te(n.onClick,[e,r,this],this)}}function Z2(t,e,n,r,i){const s=J2(r,t,e,n),a=eS(i,r,e.lineHeight);return{itemWidth:s,itemHeight:a}}function J2(t,e,n,r){let i=t.text;return i&&typeof i!="string"&&(i=i.reduce((s,a)=>s.length>a.length?s:a)),e+n.size/2+r.measureText(i).width}function eS(t,e,n){let r=t;return typeof e.text!="string"&&(r=vg(e,n)),r}function vg(t,e){const n=t.text?t.text.length:0;return e*n}function tS(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var bg={id:"legend",_element:Fh,start(t,e,n){const r=t.legend=new Fh({ctx:t.ctx,options:n,chart:t});it.configure(t,r,n),it.addBox(t,r)},stop(t){it.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,n){const r=t.legend;it.configure(t,r,n),r.options=n},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,n){const r=e.datasetIndex,i=n.chart;i.isDatasetVisible(r)?(i.hide(r),e.hidden=!0):(i.show(r),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:n,pointStyle:r,textAlign:i,color:s,useBorderRadius:a,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(l=>{const u=l.controller.getStyle(n?0:void 0),d=ct(u.borderWidth);return{text:e[l.index].label,fillStyle:u.backgroundColor,fontColor:s,hidden:!l.visible,lineCap:u.borderCapStyle,lineDash:u.borderDash,lineDashOffset:u.borderDashOffset,lineJoin:u.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:u.borderColor,pointStyle:r||u.pointStyle,rotation:u.rotation,textAlign:i||u.textAlign,borderRadius:a&&(o||u.borderRadius),datasetIndex:l.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class wg extends Vt{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,n){const r=this.options;if(this.left=0,this.top=0,!r.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=n;const i=ge(r.text)?r.text.length:1;this._padding=ct(r.padding);const s=i*Pe(r.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:n,left:r,bottom:i,right:s,options:a}=this,o=a.align;let l=0,u,d,h;return this.isHorizontal()?(d=je(o,r,s),h=n+e,u=s-r):(a.position==="left"?(d=r+e,h=je(o,i,n),l=fe*-.5):(d=s-e,h=je(o,n,i),l=fe*.5),u=i-n),{titleX:d,titleY:h,maxWidth:u,rotation:l}}draw(){const e=this.ctx,n=this.options;if(!n.display)return;const r=Pe(n.font),s=r.lineHeight/2+this._padding.top,{titleX:a,titleY:o,maxWidth:l,rotation:u}=this._drawArgs(s);Xi(e,n.text,0,0,r,{color:n.color,maxWidth:l,rotation:u,textAlign:su(n.align),textBaseline:"middle",translation:[a,o]})}}function nS(t,e){const n=new wg({ctx:t.ctx,options:e,chart:t});it.configure(t,n,e),it.addBox(t,n),t.titleBlock=n}var rS={id:"title",_element:wg,start(t,e,n){nS(t,n)},stop(t){const e=t.titleBlock;it.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,n){const r=t.titleBlock;it.configure(t,r,n),r.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const di={average(t){if(!t.length)return!1;let e,n,r=new Set,i=0,s=0;for(e=0,n=t.length;e<n;++e){const o=t[e].element;if(o&&o.hasValue()){const l=o.tooltipPosition();r.add(l.x),i+=l.y,++s}}return s===0||r.size===0?!1:{x:[...r].reduce((o,l)=>o+l)/r.size,y:i/s}},nearest(t,e){if(!t.length)return!1;let n=e.x,r=e.y,i=Number.POSITIVE_INFINITY,s,a,o;for(s=0,a=t.length;s<a;++s){const l=t[s].element;if(l&&l.hasValue()){const u=l.getCenterPoint(),d=gb(e,u);d<i&&(i=d,o=l)}}if(o){const l=o.tooltipPosition();n=l.x,r=l.y}return{x:n,y:r}}};function _t(t,e){return e&&(ge(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function Rt(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function iS(t,e){const{element:n,datasetIndex:r,index:i}=e,s=t.getDatasetMeta(r).controller,{label:a,value:o}=s.getLabelAndValue(i);return{chart:t,label:a,parsed:s.getParsed(i),raw:t.data.datasets[r].data[i],formattedValue:o,dataset:s.getDataset(),dataIndex:i,datasetIndex:r,element:n}}function Ah(t,e){const n=t.chart.ctx,{body:r,footer:i,title:s}=t,{boxWidth:a,boxHeight:o}=e,l=Pe(e.bodyFont),u=Pe(e.titleFont),d=Pe(e.footerFont),h=s.length,f=i.length,m=r.length,x=ct(e.padding);let y=x.height,b=0,g=r.reduce((w,_)=>w+_.before.length+_.lines.length+_.after.length,0);if(g+=t.beforeBody.length+t.afterBody.length,h&&(y+=h*u.lineHeight+(h-1)*e.titleSpacing+e.titleMarginBottom),g){const w=e.displayColors?Math.max(o,l.lineHeight):l.lineHeight;y+=m*w+(g-m)*l.lineHeight+(g-1)*e.bodySpacing}f&&(y+=e.footerMarginTop+f*d.lineHeight+(f-1)*e.footerSpacing);let p=0;const v=function(w){b=Math.max(b,n.measureText(w).width+p)};return n.save(),n.font=u.string,X(t.title,v),n.font=l.string,X(t.beforeBody.concat(t.afterBody),v),p=e.displayColors?a+2+e.boxPadding:0,X(r,w=>{X(w.before,v),X(w.lines,v),X(w.after,v)}),p=0,n.font=d.string,X(t.footer,v),n.restore(),b+=x.width,{width:b,height:y}}function sS(t,e){const{y:n,height:r}=e;return n<r/2?"top":n>t.height-r/2?"bottom":"center"}function aS(t,e,n,r){const{x:i,width:s}=r,a=n.caretSize+n.caretPadding;if(t==="left"&&i+s+a>e.width||t==="right"&&i-s-a<0)return!0}function oS(t,e,n,r){const{x:i,width:s}=n,{width:a,chartArea:{left:o,right:l}}=t;let u="center";return r==="center"?u=i<=(o+l)/2?"left":"right":i<=s/2?u="left":i>=a-s/2&&(u="right"),aS(u,t,e,n)&&(u="center"),u}function Ih(t,e,n){const r=n.yAlign||e.yAlign||sS(t,n);return{xAlign:n.xAlign||e.xAlign||oS(t,e,n,r),yAlign:r}}function lS(t,e){let{x:n,width:r}=t;return e==="right"?n-=r:e==="center"&&(n-=r/2),n}function cS(t,e,n){let{y:r,height:i}=t;return e==="top"?r+=n:e==="bottom"?r-=i+n:r-=i/2,r}function Uh(t,e,n,r){const{caretSize:i,caretPadding:s,cornerRadius:a}=t,{xAlign:o,yAlign:l}=n,u=i+s,{topLeft:d,topRight:h,bottomLeft:f,bottomRight:m}=Sr(a);let x=lS(e,o);const y=cS(e,l,u);return l==="center"?o==="left"?x+=u:o==="right"&&(x-=u):o==="left"?x-=Math.max(d,f)+i:o==="right"&&(x+=Math.max(h,m)+i),{x:He(x,0,r.width-e.width),y:He(y,0,r.height-e.height)}}function Rs(t,e,n){const r=ct(n.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-r.right:t.x+r.left}function Hh(t){return _t([],Rt(t))}function uS(t,e,n){return Ir(t,{tooltip:e,tooltipItems:n,type:"tooltip"})}function Bh(t,e){const n=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return n?t.override(n):t}const Sg={beforeTitle:Et,title(t){if(t.length>0){const e=t[0],n=e.chart.data.labels,r=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(r>0&&e.dataIndex<r)return n[e.dataIndex]}return""},afterTitle:Et,beforeBody:Et,beforeLabel:Et,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const n=t.formattedValue;return q(n)||(e+=n),e},labelColor(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const n=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Et,afterBody:Et,beforeFooter:Et,footer:Et,afterFooter:Et};function Fe(t,e,n,r){const i=t[e].call(n,r);return typeof i>"u"?Sg[e].call(n,r):i}class tc extends Vt{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const n=this.chart,r=this.options.setContext(this.getContext()),i=r.enabled&&n.options.animation&&r.animations,s=new ag(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=uS(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,n){const{callbacks:r}=n,i=Fe(r,"beforeTitle",this,e),s=Fe(r,"title",this,e),a=Fe(r,"afterTitle",this,e);let o=[];return o=_t(o,Rt(i)),o=_t(o,Rt(s)),o=_t(o,Rt(a)),o}getBeforeBody(e,n){return Hh(Fe(n.callbacks,"beforeBody",this,e))}getBody(e,n){const{callbacks:r}=n,i=[];return X(e,s=>{const a={before:[],lines:[],after:[]},o=Bh(r,s);_t(a.before,Rt(Fe(o,"beforeLabel",this,s))),_t(a.lines,Fe(o,"label",this,s)),_t(a.after,Rt(Fe(o,"afterLabel",this,s))),i.push(a)}),i}getAfterBody(e,n){return Hh(Fe(n.callbacks,"afterBody",this,e))}getFooter(e,n){const{callbacks:r}=n,i=Fe(r,"beforeFooter",this,e),s=Fe(r,"footer",this,e),a=Fe(r,"afterFooter",this,e);let o=[];return o=_t(o,Rt(i)),o=_t(o,Rt(s)),o=_t(o,Rt(a)),o}_createItems(e){const n=this._active,r=this.chart.data,i=[],s=[],a=[];let o=[],l,u;for(l=0,u=n.length;l<u;++l)o.push(iS(this.chart,n[l]));return e.filter&&(o=o.filter((d,h,f)=>e.filter(d,h,f,r))),e.itemSort&&(o=o.sort((d,h)=>e.itemSort(d,h,r))),X(o,d=>{const h=Bh(e.callbacks,d);i.push(Fe(h,"labelColor",this,d)),s.push(Fe(h,"labelPointStyle",this,d)),a.push(Fe(h,"labelTextColor",this,d))}),this.labelColors=i,this.labelPointStyles=s,this.labelTextColors=a,this.dataPoints=o,o}update(e,n){const r=this.options.setContext(this.getContext()),i=this._active;let s,a=[];if(!i.length)this.opacity!==0&&(s={opacity:0});else{const o=di[r.position].call(this,i,this._eventPosition);a=this._createItems(r),this.title=this.getTitle(a,r),this.beforeBody=this.getBeforeBody(a,r),this.body=this.getBody(a,r),this.afterBody=this.getAfterBody(a,r),this.footer=this.getFooter(a,r);const l=this._size=Ah(this,r),u=Object.assign({},o,l),d=Ih(this.chart,r,u),h=Uh(r,u,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:h.x,y:h.y,width:l.width,height:l.height,caretX:o.x,caretY:o.y}}this._tooltipItems=a,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&r.external&&r.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(e,n,r,i){const s=this.getCaretPosition(e,r,i);n.lineTo(s.x1,s.y1),n.lineTo(s.x2,s.y2),n.lineTo(s.x3,s.y3)}getCaretPosition(e,n,r){const{xAlign:i,yAlign:s}=this,{caretSize:a,cornerRadius:o}=r,{topLeft:l,topRight:u,bottomLeft:d,bottomRight:h}=Sr(o),{x:f,y:m}=e,{width:x,height:y}=n;let b,g,p,v,w,_;return s==="center"?(w=m+y/2,i==="left"?(b=f,g=b-a,v=w+a,_=w-a):(b=f+x,g=b+a,v=w-a,_=w+a),p=b):(i==="left"?g=f+Math.max(l,d)+a:i==="right"?g=f+x-Math.max(u,h)-a:g=this.caretX,s==="top"?(v=m,w=v-a,b=g-a,p=g+a):(v=m+y,w=v+a,b=g+a,p=g-a),_=v),{x1:b,x2:g,x3:p,y1:v,y2:w,y3:_}}drawTitle(e,n,r){const i=this.title,s=i.length;let a,o,l;if(s){const u=_r(r.rtl,this.x,this.width);for(e.x=Rs(this,r.titleAlign,r),n.textAlign=u.textAlign(r.titleAlign),n.textBaseline="middle",a=Pe(r.titleFont),o=r.titleSpacing,n.fillStyle=r.titleColor,n.font=a.string,l=0;l<s;++l)n.fillText(i[l],u.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+o,l+1===s&&(e.y+=r.titleMarginBottom-o)}}_drawColorBox(e,n,r,i,s){const a=this.labelColors[r],o=this.labelPointStyles[r],{boxHeight:l,boxWidth:u}=s,d=Pe(s.bodyFont),h=Rs(this,"left",s),f=i.x(h),m=l<d.lineHeight?(d.lineHeight-l)/2:0,x=n.y+m;if(s.usePointStyle){const y={radius:Math.min(u,l)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},b=i.leftForLtr(f,u)+u/2,g=x+l/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,ih(e,y,b,g),e.strokeStyle=a.borderColor,e.fillStyle=a.backgroundColor,ih(e,y,b,g)}else{e.lineWidth=B(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,e.strokeStyle=a.borderColor,e.setLineDash(a.borderDash||[]),e.lineDashOffset=a.borderDashOffset||0;const y=i.leftForLtr(f,u),b=i.leftForLtr(i.xPlus(f,1),u-2),g=Sr(a.borderRadius);Object.values(g).some(p=>p!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,Da(e,{x:y,y:x,w:u,h:l,radius:g}),e.fill(),e.stroke(),e.fillStyle=a.backgroundColor,e.beginPath(),Da(e,{x:b,y:x+1,w:u-2,h:l-2,radius:g}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(y,x,u,l),e.strokeRect(y,x,u,l),e.fillStyle=a.backgroundColor,e.fillRect(b,x+1,u-2,l-2))}e.fillStyle=this.labelTextColors[r]}drawBody(e,n,r){const{body:i}=this,{bodySpacing:s,bodyAlign:a,displayColors:o,boxHeight:l,boxWidth:u,boxPadding:d}=r,h=Pe(r.bodyFont);let f=h.lineHeight,m=0;const x=_r(r.rtl,this.x,this.width),y=function(j){n.fillText(j,x.x(e.x+m),e.y+f/2),e.y+=f+s},b=x.textAlign(a);let g,p,v,w,_,S,N;for(n.textAlign=a,n.textBaseline="middle",n.font=h.string,e.x=Rs(this,b,r),n.fillStyle=r.bodyColor,X(this.beforeBody,y),m=o&&b!=="right"?a==="center"?u/2+d:u+2+d:0,w=0,S=i.length;w<S;++w){for(g=i[w],p=this.labelTextColors[w],n.fillStyle=p,X(g.before,y),v=g.lines,o&&v.length&&(this._drawColorBox(n,e,w,x,r),f=Math.max(h.lineHeight,l)),_=0,N=v.length;_<N;++_)y(v[_]),f=h.lineHeight;X(g.after,y)}m=0,f=h.lineHeight,X(this.afterBody,y),e.y-=s}drawFooter(e,n,r){const i=this.footer,s=i.length;let a,o;if(s){const l=_r(r.rtl,this.x,this.width);for(e.x=Rs(this,r.footerAlign,r),e.y+=r.footerMarginTop,n.textAlign=l.textAlign(r.footerAlign),n.textBaseline="middle",a=Pe(r.footerFont),n.fillStyle=r.footerColor,n.font=a.string,o=0;o<s;++o)n.fillText(i[o],l.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+r.footerSpacing}}drawBackground(e,n,r,i){const{xAlign:s,yAlign:a}=this,{x:o,y:l}=e,{width:u,height:d}=r,{topLeft:h,topRight:f,bottomLeft:m,bottomRight:x}=Sr(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(o+h,l),a==="top"&&this.drawCaret(e,n,r,i),n.lineTo(o+u-f,l),n.quadraticCurveTo(o+u,l,o+u,l+f),a==="center"&&s==="right"&&this.drawCaret(e,n,r,i),n.lineTo(o+u,l+d-x),n.quadraticCurveTo(o+u,l+d,o+u-x,l+d),a==="bottom"&&this.drawCaret(e,n,r,i),n.lineTo(o+m,l+d),n.quadraticCurveTo(o,l+d,o,l+d-m),a==="center"&&s==="left"&&this.drawCaret(e,n,r,i),n.lineTo(o,l+h),n.quadraticCurveTo(o,l,o+h,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(e){const n=this.chart,r=this.$animations,i=r&&r.x,s=r&&r.y;if(i||s){const a=di[e.position].call(this,this._active,this._eventPosition);if(!a)return;const o=this._size=Ah(this,e),l=Object.assign({},a,this._size),u=Ih(n,e,l),d=Uh(e,l,u,n);(i._to!==d.x||s._to!==d.y)&&(this.xAlign=u.xAlign,this.yAlign=u.yAlign,this.width=o.width,this.height=o.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const n=this.options.setContext(this.getContext());let r=this.opacity;if(!r)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},s={x:this.x,y:this.y};r=Math.abs(r)<.001?0:r;const a=ct(n.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&o&&(e.save(),e.globalAlpha=r,this.drawBackground(s,e,i,n),ig(e,n.textDirection),s.y+=a.top,this.drawTitle(s,e,n),this.drawBody(s,e,n),this.drawFooter(s,e,n),sg(e,n.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,n){const r=this._active,i=e.map(({datasetIndex:o,index:l})=>{const u=this.chart.getDatasetMeta(o);if(!u)throw new Error("Cannot find a dataset at index "+o);return{datasetIndex:o,element:u.data[l],index:l}}),s=!Ma(r,i),a=this._positionChanged(i,n);(s||a)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,n,r=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,s=this._active||[],a=this._getActiveElements(e,s,n,r),o=this._positionChanged(a,e),l=n||!Ma(a,s)||o;return l&&(this._active=a,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,n))),l}_getActiveElements(e,n,r,i){const s=this.options;if(e.type==="mouseout")return[];if(!i)return n.filter(o=>this.chart.data.datasets[o.datasetIndex]&&this.chart.getDatasetMeta(o.datasetIndex).controller.getParsed(o.index)!==void 0);const a=this.chart.getElementsAtEventForMode(e,s.mode,s,r);return s.reverse&&a.reverse(),a}_positionChanged(e,n){const{caretX:r,caretY:i,options:s}=this,a=di[s.position].call(this,e,n);return a!==!1&&(r!==a.x||i!==a.y)}}F(tc,"positioners",di);var _g={id:"tooltip",_element:tc,positioners:di,afterInit(t,e,n){n&&(t.tooltip=new tc({chart:t,options:n}))},beforeUpdate(t,e,n){t.tooltip&&t.tooltip.initialize(n)},reset(t,e,n){t.tooltip&&t.tooltip.initialize(n)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const n={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",n)}},afterEvent(t,e){if(t.tooltip){const n=e.replay;t.tooltip.handleEvent(e.event,n,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Sg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const dS=(t,e,n,r)=>(typeof e=="string"?(n=t.push(e)-1,r.unshift({index:n,label:e})):isNaN(e)&&(n=null),n);function hS(t,e,n,r){const i=t.indexOf(e);if(i===-1)return dS(t,e,n,r);const s=t.lastIndexOf(e);return i!==s?n:i}const fS=(t,e)=>t===null?null:He(Math.round(t),0,e);function Wh(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class nc extends Ur{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const n=this._addedLabels;if(n.length){const r=this.getLabels();for(const{index:i,label:s}of n)r[i]===s&&r.splice(i,1);this._addedLabels=[]}super.init(e)}parse(e,n){if(q(e))return null;const r=this.getLabels();return n=isFinite(n)&&r[n]===e?n:hS(r,e,W(n,e),this._addedLabels),fS(n,r.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:n}=this.getUserBounds();let{min:r,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(r=0),n||(i=this.getLabels().length-1)),this.min=r,this.max=i}buildTicks(){const e=this.min,n=this.max,r=this.options.offset,i=[];let s=this.getLabels();s=e===0&&n===s.length-1?s:s.slice(e,n+1),this._valueRange=Math.max(s.length-(r?0:1),1),this._startValue=this.min-(r?.5:0);for(let a=e;a<=n;a++)i.push({value:a});return i}getLabelForValue(e){return Wh.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const n=this.ticks;return e<0||e>n.length-1?null:this.getPixelForValue(n[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}F(nc,"id","category"),F(nc,"defaults",{ticks:{callback:Wh}});function mS(t,e){const n=[],{bounds:i,step:s,min:a,max:o,precision:l,count:u,maxTicks:d,maxDigits:h,includeBounds:f}=t,m=s||1,x=d-1,{min:y,max:b}=e,g=!q(a),p=!q(o),v=!q(u),w=(b-y)/(h+1);let _=Kd((b-y)/x/m)*m,S,N,j,$;if(_<1e-14&&!g&&!p)return[{value:y},{value:b}];$=Math.ceil(b/_)-Math.floor(y/_),$>x&&(_=Kd($*_/x/m)*m),q(l)||(S=Math.pow(10,l),_=Math.ceil(_*S)/S),i==="ticks"?(N=Math.floor(y/_)*_,j=Math.ceil(b/_)*_):(N=y,j=b),g&&p&&s&&fb((o-a)/s,_/1e3)?($=Math.round(Math.min((o-a)/_,d)),_=(o-a)/$,N=a,j=o):v?(N=g?a:N,j=p?o:j,$=u-1,_=(j-N)/$):($=(j-N)/_,Xs($,Math.round($),_/1e3)?$=Math.round($):$=Math.ceil($));const M=Math.max(Gd(_),Gd(N));S=Math.pow(10,q(l)?M:l),N=Math.round(N*S)/S,j=Math.round(j*S)/S;let E=0;for(g&&(f&&N!==a?(n.push({value:a}),N<a&&E++,Xs(Math.round((N+E*_)*S)/S,a,Vh(a,w,t))&&E++):N<a&&E++);E<$;++E){const L=Math.round((N+E*_)*S)/S;if(p&&L>o)break;n.push({value:L})}return p&&f&&j!==o?n.length&&Xs(n[n.length-1].value,o,Vh(o,w,t))?n[n.length-1].value=o:n.push({value:o}):(!p||j===o)&&n.push({value:j}),n}function Vh(t,e,{horizontal:n,minRotation:r}){const i=At(r),s=(n?Math.sin(i):Math.cos(i))||.001,a=.75*e*(""+t).length;return Math.min(e/s,a)}class pS extends Ur{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,n){return q(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:n,maxDefined:r}=this.getUserBounds();let{min:i,max:s}=this;const a=l=>i=n?i:l,o=l=>s=r?s:l;if(e){const l=xn(i),u=xn(s);l<0&&u<0?o(0):l>0&&u>0&&a(0)}if(i===s){let l=s===0?1:Math.abs(s*.05);o(s+l),e||a(i-l)}this.min=i,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:n,stepSize:r}=e,i;return r?(i=Math.ceil(this.max/r)-Math.floor(this.min/r)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${r} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,n=e.ticks;let r=this.getTickLimit();r=Math.max(2,r);const i={maxTicks:r,bounds:e.bounds,min:e.min,max:e.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},s=this._range||this,a=mS(i,s);return e.bounds==="ticks"&&mb(a,this,"value"),e.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const e=this.ticks;let n=this.min,r=this.max;if(super.configure(),this.options.offset&&e.length){const i=(r-n)/Math.max(e.length-1,1)/2;n-=i,r+=i}this._startValue=n,this._endValue=r,this._valueRange=r-n}getLabelForValue(e){return au(e,this.chart.options.locale,this.options.ticks.format)}}class rc extends pS{determineDataLimits(){const{min:e,max:n}=this.getMinMax(!0);this.min=lt(e)?e:0,this.max=lt(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),n=e?this.width:this.height,r=At(this.options.ticks.minRotation),i=(e?Math.sin(r):Math.cos(r))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,s.lineHeight/i))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}F(rc,"id","linear"),F(rc,"defaults",{ticks:{callback:Gp.formatters.numeric}});const oo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Ie=Object.keys(oo);function Yh(t,e){return t-e}function Xh(t,e){if(q(e))return null;const n=t._adapter,{parser:r,round:i,isoWeekday:s}=t._parseOpts;let a=e;return typeof r=="function"&&(a=r(a)),lt(a)||(a=typeof r=="string"?n.parse(a,r):n.parse(a)),a===null?null:(i&&(a=i==="week"&&(Ta(s)||s===!0)?n.startOf(a,"isoWeek",s):n.startOf(a,i)),+a)}function Qh(t,e,n,r){const i=Ie.length;for(let s=Ie.indexOf(t);s<i-1;++s){const a=oo[Ie[s]],o=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((n-e)/(o*a.size))<=r)return Ie[s]}return Ie[i-1]}function gS(t,e,n,r,i){for(let s=Ie.length-1;s>=Ie.indexOf(n);s--){const a=Ie[s];if(oo[a].common&&t._adapter.diff(i,r,a)>=e-1)return a}return Ie[n?Ie.indexOf(n):0]}function xS(t){for(let e=Ie.indexOf(t)+1,n=Ie.length;e<n;++e)if(oo[Ie[e]].common)return Ie[e]}function Kh(t,e,n){if(!n)t[e]=!0;else if(n.length){const{lo:r,hi:i}=iu(n,e),s=n[r]>=e?n[r]:n[i];t[s]=!0}}function yS(t,e,n,r){const i=t._adapter,s=+i.startOf(e[0].value,r),a=e[e.length-1].value;let o,l;for(o=s;o<=a;o=+i.add(o,1,r))l=n[o],l>=0&&(e[l].major=!0);return e}function Gh(t,e,n){const r=[],i={},s=e.length;let a,o;for(a=0;a<s;++a)o=e[a],i[o]=a,r.push({value:o,major:!1});return s===0||!n?r:yS(t,r,i,n)}class Fa extends Ur{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,n={}){const r=e.time||(e.time={}),i=this._adapter=new Tw._date(e.adapters.date);i.init(n),wi(r.displayFormats,i.formats()),this._parseOpts={parser:r.parser,round:r.round,isoWeekday:r.isoWeekday},super.init(e),this._normalized=n.normalized}parse(e,n){return e===void 0?null:Xh(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,n=this._adapter,r=e.time.unit||"day";let{min:i,max:s,minDefined:a,maxDefined:o}=this.getUserBounds();function l(u){!a&&!isNaN(u.min)&&(i=Math.min(i,u.min)),!o&&!isNaN(u.max)&&(s=Math.max(s,u.max))}(!a||!o)&&(l(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=lt(i)&&!isNaN(i)?i:+n.startOf(Date.now(),r),s=lt(s)&&!isNaN(s)?s:+n.endOf(Date.now(),r)+1,this.min=Math.min(i,s-1),this.max=Math.max(i+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;return e.length&&(n=e[0],r=e[e.length-1]),{min:n,max:r}}buildTicks(){const e=this.options,n=e.time,r=e.ticks,i=r.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const s=this.min,a=this.max,o=vb(i,s,a);return this._unit=n.unit||(r.autoSkip?Qh(n.minUnit,this.min,this.max,this._getLabelCapacity(s)):gS(this,o.length,n.minUnit,this.min,this.max)),this._majorUnit=!r.major.enabled||this._unit==="year"?void 0:xS(this._unit),this.initOffsets(i),e.reverse&&o.reverse(),Gh(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let n=0,r=0,i,s;this.options.offset&&e.length&&(i=this.getDecimalForValue(e[0]),e.length===1?n=1-i:n=(this.getDecimalForValue(e[1])-i)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?r=s:r=(s-this.getDecimalForValue(e[e.length-2]))/2);const a=e.length<3?.5:.25;n=He(n,0,a),r=He(r,0,a),this._offsets={start:n,end:r,factor:1/(n+1+r)}}_generate(){const e=this._adapter,n=this.min,r=this.max,i=this.options,s=i.time,a=s.unit||Qh(s.minUnit,n,r,this._getLabelCapacity(n)),o=W(i.ticks.stepSize,1),l=a==="week"?s.isoWeekday:!1,u=Ta(l)||l===!0,d={};let h=n,f,m;if(u&&(h=+e.startOf(h,"isoWeek",l)),h=+e.startOf(h,u?"day":a),e.diff(r,n,a)>1e5*o)throw new Error(n+" and "+r+" are too far apart with stepSize of "+o+" "+a);const x=i.ticks.source==="data"&&this.getDataTimestamps();for(f=h,m=0;f<r;f=+e.add(f,o,a),m++)Kh(d,f,x);return(f===r||i.bounds==="ticks"||m===1)&&Kh(d,f,x),Object.keys(d).sort(Yh).map(y=>+y)}getLabelForValue(e){const n=this._adapter,r=this.options.time;return r.tooltipFormat?n.format(e,r.tooltipFormat):n.format(e,r.displayFormats.datetime)}format(e,n){const i=this.options.time.displayFormats,s=this._unit,a=n||i[s];return this._adapter.format(e,a)}_tickFormatFunction(e,n,r,i){const s=this.options,a=s.ticks.callback;if(a)return te(a,[e,n,r],this);const o=s.time.displayFormats,l=this._unit,u=this._majorUnit,d=l&&o[l],h=u&&o[u],f=r[n],m=u&&h&&f&&f.major;return this._adapter.format(e,i||(m?h:d))}generateTickLabels(e){let n,r,i;for(n=0,r=e.length;n<r;++n)i=e[n],i.label=this._tickFormatFunction(i.value,n,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const n=this._offsets,r=this.getDecimalForValue(e);return this.getPixelForDecimal((n.start+r)*n.factor)}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return this.min+r*(this.max-this.min)}_getLabelSize(e){const n=this.options.ticks,r=this.ctx.measureText(e).width,i=At(this.isHorizontal()?n.maxRotation:n.minRotation),s=Math.cos(i),a=Math.sin(i),o=this._resolveTickFontOptions(0).size;return{w:r*s+o*a,h:r*a+o*s}}_getLabelCapacity(e){const n=this.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,s=this._tickFormatFunction(e,0,Gh(this,[e],this._majorUnit),i),a=this._getLabelSize(s),o=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return o>0?o:1}getDataTimestamps(){let e=this._cache.data||[],n,r;if(e.length)return e;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,r=i.length;n<r;++n)e=e.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let n,r;if(e.length)return e;const i=this.getLabels();for(n=0,r=i.length;n<r;++n)e.push(Xh(this,i[n]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return Yp(e.sort(Yh))}}F(Fa,"id","time"),F(Fa,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Ds(t,e,n){let r=0,i=t.length-1,s,a,o,l;n?(e>=t[r].pos&&e<=t[i].pos&&({lo:r,hi:i}=ql(t,"pos",e)),{pos:s,time:o}=t[r],{pos:a,time:l}=t[i]):(e>=t[r].time&&e<=t[i].time&&({lo:r,hi:i}=ql(t,"time",e)),{time:s,pos:o}=t[r],{time:a,pos:l}=t[i]);const u=a-s;return u?o+(l-o)*(e-s)/u:o}class qh extends Fa{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(e);this._minPos=Ds(n,this.min),this._tableRange=Ds(n,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:n,max:r}=this,i=[],s=[];let a,o,l,u,d;for(a=0,o=e.length;a<o;++a)u=e[a],u>=n&&u<=r&&i.push(u);if(i.length<2)return[{time:n,pos:0},{time:r,pos:1}];for(a=0,o=i.length;a<o;++a)d=i[a+1],l=i[a-1],u=i[a],Math.round((d+l)/2)!==u&&s.push({time:u,pos:a/(o-1)});return s}_generate(){const e=this.min,n=this.max;let r=super.getDataTimestamps();return(!r.includes(e)||!r.length)&&r.splice(0,0,e),(!r.includes(n)||r.length===1)&&r.push(n),r.sort((i,s)=>i-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const n=this.getDataTimestamps(),r=this.getLabelTimestamps();return n.length&&r.length?e=this.normalize(n.concat(r)):e=n.length?n:r,e=this._cache.all=e,e}getDecimalForValue(e){return(Ds(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const n=this._offsets,r=this.getDecimalForPixel(e)/n.factor-n.end;return Ds(this._table,r*this._tableRange+this._minPos,!0)}}F(qh,"id","timeseries"),F(qh,"defaults",Fa.defaults);const Ng="label";function Zh(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function vS(t,e){const n=t.options;n&&e&&Object.assign(n,e)}function kg(t,e){t.labels=e}function jg(t,e){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ng;const r=[];t.datasets=e.map(i=>{const s=t.datasets.find(a=>a[n]===i[n]);return!s||!i.data||r.includes(s)?{...i}:(r.push(s),Object.assign(s,i),s)})}function bS(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ng;const n={labels:[],datasets:[]};return kg(n,t.labels),jg(n,t.datasets,e),n}function wS(t,e){const{height:n=150,width:r=300,redraw:i=!1,datasetIdKey:s,type:a,data:o,options:l,plugins:u=[],fallbackContent:d,updateMode:h,...f}=t,m=k.useRef(null),x=k.useRef(null),y=()=>{m.current&&(x.current=new ns(m.current,{type:a,data:bS(o,s),options:l&&{...l},plugins:u}),Zh(e,x.current))},b=()=>{Zh(e,null),x.current&&(x.current.destroy(),x.current=null)};return k.useEffect(()=>{!i&&x.current&&l&&vS(x.current,l)},[i,l]),k.useEffect(()=>{!i&&x.current&&kg(x.current.config.data,o.labels)},[i,o.labels]),k.useEffect(()=>{!i&&x.current&&o.datasets&&jg(x.current.config.data,o.datasets,s)},[i,o.datasets]),k.useEffect(()=>{x.current&&(i?(b(),setTimeout(y)):x.current.update(h))},[i,l,o.labels,o.datasets,h]),k.useEffect(()=>{x.current&&(b(),setTimeout(y))},[a]),k.useEffect(()=>(y(),()=>b()),[]),gt.createElement("canvas",{ref:m,role:"img",height:n,width:r,...f},d)}const SS=k.forwardRef(wS);function Cg(t,e){return ns.register(e),k.forwardRef((n,r)=>gt.createElement(SS,{...n,ref:r,type:t}))}const _S=Cg("bar",Qs),NS=Cg("doughnut",li);ns.register(ui,_g,bg);function kS(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n}=Xe(),{theme:r}=me(),i=k.useRef(null),s=t.currentUnits,a=e,o=s+a,l=o>0?a/o*100:0;k.useEffect(()=>{const h=i.current;if(h){const f=h.ctx,m=f.createRadialGradient(200,200,50,200,200,150);m.addColorStop(0,"#667eea"),m.addColorStop(.3,"#764ba2"),m.addColorStop(.6,"#667eea"),m.addColorStop(1,"#f093fb");const x=f.createRadialGradient(200,200,50,200,200,150);x.addColorStop(0,"#ff9a9e"),x.addColorStop(.3,"#fecfef"),x.addColorStop(.6,"#fecfef"),x.addColorStop(1,"#ffc3a0"),h.data.datasets[0].backgroundColor=[m,x],h.update()}},[s,a]);const u={labels:[`Remaining ${n()}`,`Used ${n()}`],datasets:[{data:[s,a],backgroundColor:["linear-gradient(135deg, #667eea 0%, #764ba2 100%)","linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffc3a0 100%)"],borderColor:["rgba(255, 255, 255, 0.9)","rgba(255, 255, 255, 0.9)"],borderWidth:4,cutout:"78%",borderRadius:12,borderJoinStyle:"round",hoverBorderWidth:6,hoverBorderColor:["rgba(255, 255, 255, 1)","rgba(255, 255, 255, 1)"],shadowOffsetX:3,shadowOffsetY:3,shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.1)"}]},d={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(h){const f=h.label||"",m=h.parsed,x=o>0?(m/o*100).toFixed(1):0;return`${f}: ${m.toFixed(2)} (${x}%)`}}}},animation:{animateRotate:!0,animateScale:!0,duration:2e3,easing:"easeInOutCubic",delay:h=>h.dataIndex*200},interaction:{intersect:!1,mode:"nearest"},elements:{arc:{borderWidth:4,hoverBorderWidth:6,borderSkipped:!1,borderAlign:"inner"}},layout:{padding:{top:20,bottom:20,left:20,right:20}}};return c.jsxs("div",{className:"relative",children:[c.jsxs("div",{className:"relative h-[32rem] mb-8 flex items-center justify-center",children:[c.jsx("div",{className:`absolute top-8 left-8 w-20 h-20 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-xl animate-pulse`}),c.jsx("div",{className:`absolute bottom-8 right-8 w-24 h-24 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-2xl animate-pulse`,style:{animationDelay:"1s"}}),c.jsx("div",{className:`absolute top-1/2 left-4 w-16 h-16 bg-gradient-to-r ${r.gradient} rounded-full opacity-10 blur-lg animate-pulse`,style:{animationDelay:"2s"}}),c.jsx("div",{className:`absolute top-1/4 right-8 w-12 h-12 bg-gradient-to-r ${r.gradient} rounded-full opacity-25 blur-md animate-pulse`,style:{animationDelay:"0.5s"}}),c.jsxs("div",{className:"relative h-full w-full max-w-lg max-h-lg flex items-center justify-center",children:[c.jsx(NS,{ref:i,data:u,options:d}),c.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 w-64 h-64 bg-gradient-to-r from-indigo-400 via-purple-500 to-pink-500 rounded-full blur-2xl opacity-40 animate-pulse"}),c.jsxs("div",{className:`relative w-64 h-64 ${r.card}/50 border-4 ${r.border}/40 backdrop-blur-xl rounded-full shadow-2xl flex items-center justify-center`,children:[c.jsx("div",{className:`absolute inset-3 ${r.secondary}/70 rounded-full`}),c.jsxs("div",{className:"relative text-center z-10",children:[c.jsx("div",{className:"mb-2 flex justify-center",children:c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse"}),c.jsx(Re,{className:"relative h-8 w-8 text-transparent bg-gradient-to-r from-yellow-500 to-orange-600 bg-clip-text animate-bounce",style:{animationDuration:"2s"}})]})}),c.jsxs("div",{className:"relative mb-2",children:[c.jsx("div",{className:`text-4xl font-black ${r.text} drop-shadow-lg`,children:s.toFixed(2)}),c.jsxs("div",{className:`text-sm font-bold ${r.textSecondary} mt-1 tracking-wide`,children:[n()," Left"]})]}),c.jsx("div",{className:"mt-1",children:c.jsxs("div",{className:`text-base font-bold tracking-tight ${r.textSecondary} drop-shadow-lg`,children:[l.toFixed(1),"% Used"]})})]}),c.jsx("div",{className:"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-70 animate-pulse"}),c.jsx("div",{className:"absolute bottom-6 left-6 w-2 h-2 bg-gradient-to-r from-pink-400 to-orange-400 rounded-full opacity-60 animate-pulse",style:{animationDelay:"1s"}}),c.jsx("div",{className:"absolute top-1/4 right-8 w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-50 animate-pulse",style:{animationDelay:"1.5s"}}),c.jsx("div",{className:"absolute bottom-1/4 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-40 animate-pulse",style:{animationDelay:"0.5s"}})]})]})})]})]}),c.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-4 border ${r.border}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-base font-semibold mb-1 ${r.text}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${r.text}`,children:t.currentUnits.toFixed(2)}),c.jsxs("p",{className:`text-xs font-medium ${r.textSecondary}`,children:[n()," remaining"]}),c.jsxs("p",{className:`text-sm ${r.textSecondary} font-semibold mt-1`,children:["Value: ",t.currencySymbol,(t.currentUnits*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(Re,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`${r.card} rounded-2xl shadow-lg p-4 border ${r.border}`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h3",{className:`text-base font-semibold mb-1 ${r.text}`,children:"Usage Since Last Recording"}),c.jsx("p",{className:`text-2xl font-bold ${r.text}`,children:e.toFixed(2)}),c.jsxs("p",{className:`text-xs font-medium ${r.textSecondary}`,children:[n()," used"]}),c.jsxs("p",{className:`text-sm ${r.textSecondary} font-semibold mt-1`,children:["Cost: ",t.currencySymbol,(e*t.unitCost).toFixed(2)]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(pt,{className:"h-6 w-6 text-white"})})]})})]}),c.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"Total Cost"}),c.jsxs("div",{className:`text-2xl font-black ${r.text}`,children:[t.currencySymbol||"R",(a*t.unitCost).toFixed(2)]}),c.jsxs("div",{className:`text-xs font-medium ${r.textSecondary} mt-1`,children:["For ",a.toFixed(2)," ",n()," used"]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx(Jc,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-1 right-1 w-6 h-6 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`}),c.jsx("div",{className:`absolute bottom-1 left-1 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`})]}),c.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${r.card} border ${r.border} p-4 shadow-lg`,children:[c.jsx("div",{className:`absolute inset-0 ${r.secondary}`}),c.jsx("div",{className:"relative",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("div",{className:`text-xs font-semibold ${r.textSecondary} tracking-wider uppercase mb-1`,children:"Current Rate"}),c.jsxs("div",{className:`text-2xl font-black ${r.text}`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)]}),c.jsxs("div",{className:`text-xs font-medium ${r.textSecondary} mt-1`,children:["Per ",n()]})]}),c.jsx("div",{className:`p-3 rounded-xl bg-gradient-to-br ${r.gradient} shadow-lg`,children:c.jsx($1,{className:"h-6 w-6 text-white"})})]})}),c.jsx("div",{className:`absolute top-1 right-1 w-4 h-4 bg-gradient-to-r ${r.gradient} rounded-full opacity-15 blur-sm`}),c.jsx("div",{className:`absolute bottom-1 left-1 w-6 h-6 bg-gradient-to-r ${r.gradient} rounded-full opacity-20 blur-sm`})]})]}),c.jsxs("div",{className:"mt-8",children:[c.jsxs("div",{className:"flex justify-between items-center mb-4",children:[c.jsx("span",{className:`text-sm font-bold ${r.textSecondary} tracking-wide uppercase`,children:"Usage Progress"}),c.jsxs("span",{className:`text-lg font-black px-3 py-1 rounded-full ${r.secondary} ${r.text}`,children:[l.toFixed(1),"%"]})]}),c.jsx("div",{className:"relative",children:c.jsx("div",{className:`w-full ${r.secondary} rounded-full h-4 border ${r.border}`,children:c.jsx("div",{className:`h-4 rounded-full transition-all duration-1000 ease-out bg-gradient-to-r ${l>70?"from-red-500 to-red-600":l>40?"from-amber-500 to-orange-600":"from-emerald-500 to-green-600"}`,style:{width:`${Math.min(l,100)}%`}})})})]})]})}function jS(){const t=Ar(),{state:e,getDisplayUnitName:n}=Xe(),{theme:r}=me(),i=e.currentUnits,s=e.thresholdLimit;return c.jsx("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6 shadow-lg",children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx("div",{className:"p-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-lg",children:c.jsx(wr,{className:"h-6 w-6 text-white animate-pulse"})})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsxs("h3",{className:"text-xl font-bold text-amber-800 mb-2",children:["⚠️ Low ",n()," Warning!"]}),c.jsxs("div",{className:"text-sm text-amber-700 space-y-2",children:[c.jsxs("p",{className:"font-medium",children:["You have ",c.jsxs("strong",{className:"text-amber-900",children:[i.toFixed(2)," ",n()]})," remaining, which is below your threshold of ",c.jsxs("strong",{className:"text-amber-900",children:[s.toFixed(2)," ",n()]}),"."]}),c.jsxs("p",{children:["💡 ",c.jsx("strong",{children:"Time to top up!"})," Consider purchasing more ",n()," to avoid running out of power."]})]}),c.jsxs("div",{className:"mt-4 flex flex-wrap gap-3",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:"bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-lg text-sm font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",children:"🛒 Top Up Now"}),c.jsx("button",{onClick:()=>t("/settings"),className:"bg-white text-amber-700 border-2 border-amber-300 px-6 py-3 rounded-lg text-sm font-semibold hover:bg-amber-50 hover:border-amber-400 transition-all duration-200",children:"⚙️ Adjust Threshold"})]})]})]})})}function lo({title:t,content:e,position:n="top"}){const[r,i]=k.useState(!1),{theme:s}=me(),a={top:"bottom-full left-1/2 transform -translate-x-1/2 mb-2",bottom:"top-full left-1/2 transform -translate-x-1/2 mt-2",left:"right-full top-1/2 transform -translate-y-1/2 mr-2",right:"left-full top-1/2 transform -translate-y-1/2 ml-2"},o={top:`top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent ${s.border}`,bottom:`bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent ${s.border}`,left:`left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent ${s.border}`,right:`right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent ${s.border}`};return c.jsxs("div",{className:"relative inline-block",children:[c.jsx("button",{onClick:()=>i(!r),className:`p-1 rounded-full ${s.textSecondary} hover:${s.text} hover:${s.secondary} transition-all duration-200`,"aria-label":"Show help",children:c.jsx(C1,{className:"h-5 w-5"})}),r&&c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>i(!1)}),c.jsxs("div",{className:`absolute z-50 ${a[n]} w-80 max-w-sm`,children:[c.jsxs("div",{className:`${s.card} rounded-xl shadow-xl border-2 ${s.border} p-4`,children:[c.jsxs("div",{className:"flex items-center justify-between mb-3",children:[c.jsx("h3",{className:`font-bold ${s.text} text-sm`,children:t}),c.jsx("button",{onClick:()=>i(!1),className:`p-1 rounded-full ${s.textSecondary} hover:${s.text} hover:${s.secondary} transition-all duration-200`,children:c.jsx(zp,{className:"h-4 w-4"})})]}),c.jsx("div",{className:`text-sm ${s.textSecondary} leading-relaxed`,children:e})]}),c.jsx("div",{className:`absolute w-0 h-0 border-4 ${o[n]}`})]})]})]})}function Jh(){const t=Ar(),{state:e,isThresholdExceeded:n,getDisplayUnitName:r,weeklyPurchaseTotal:i,monthlyPurchaseTotal:s,weeklyUsageTotal:a,monthlyUsageTotal:o,usageSinceLastRecording:l}=Xe(),{theme:u,currentTheme:d}=me();return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${u.text}`,children:"Dashboard"}),c.jsx(lo,{title:"Dashboard Overview",content:"This is your main control center. Here you can see your current units, usage patterns, weekly/monthly totals, and quick access to all major functions. The dial shows your usage visually, and the cards below show real-time calculations.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${u.textSecondary}`,children:"Monitor your electricity usage and current meter readings"})]}),n&&c.jsx(jS,{}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${u.gradient} shadow-md`,children:c.jsx(Re,{className:"h-5 w-5 text-white"})}),"Usage Overview"]}),c.jsx("div",{className:`${u.secondary} rounded-xl p-4`,children:c.jsx(kS,{})})]}),c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${u.gradient} shadow-md`,children:c.jsx(pt,{className:"h-5 w-5 text-white"})}),"Recent Activity"]}),c.jsxs("div",{className:"space-y-3",children:[e.purchases.slice(0,3).map(h=>c.jsx("div",{className:`p-4 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsxs("p",{className:`text-sm font-semibold ${u.text}`,children:["Purchase: ",e.currencySymbol||"R",h.currency.toFixed(2)]}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(h.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["+",h.units.toFixed(2)," ",r()]})]})},h.id)),e.usageHistory.slice(0,2).map(h=>c.jsx("div",{className:`p-4 ${u.secondary} border ${u.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${u.accent} shadow-sm`,children:c.jsx(pt,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-sm font-semibold ${u.text}`,children:"Usage recorded"}),c.jsx("p",{className:`text-xs ${u.textSecondary}`,children:new Date(h.date).toLocaleDateString()})]})]}),c.jsxs("span",{className:`text-sm font-semibold ${u.text}`,children:["-",h.usage.toFixed(2)," ",r()]})]})},h.id)),e.purchases.length===0&&e.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-12 ${u.secondary} border ${u.border} rounded-xl`,children:[c.jsx("div",{className:`p-4 rounded-2xl bg-gradient-to-br ${u.gradient} w-fit mx-auto mb-4`,children:c.jsx(Re,{className:"h-12 w-12 text-white"})}),c.jsx("p",{className:`text-sm ${u.textSecondary} font-medium`,children:"No recent activity"}),c.jsx("p",{className:`text-xs ${u.textSecondary} mt-1`,children:"Start by making a purchase or recording usage"})]})]})]})]}),c.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:c.jsxs("div",{className:`${u.card} rounded-2xl shadow-lg p-6 border ${u.border}`,children:[c.jsxs("h2",{className:`text-2xl font-bold ${u.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${u.gradient} shadow-lg`,children:c.jsx(Re,{className:"h-6 w-6 text-white"})}),"Quick Actions"]}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("button",{onClick:()=>t("/purchases"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(Ye,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"Add Purchase"}),c.jsx("span",{className:"block text-sm opacity-80",children:"Top up your units"})]})]}),c.jsxs("button",{onClick:()=>t("/usage"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(pt,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"Record Usage"}),c.jsx("span",{className:"block text-sm opacity-80",children:"Track consumption"})]})]}),c.jsxs("button",{onClick:()=>t("/history"),className:`w-full flex items-center gap-4 p-4 bg-gradient-to-r ${u.gradient} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:[c.jsx(Re,{className:"h-6 w-6"}),c.jsxs("div",{className:"text-left",children:[c.jsx("span",{className:"block text-lg font-semibold",children:"View History"}),c.jsx("span",{className:"block text-sm opacity-80",children:"See all records"})]})]})]})]})})]})}const Dr=t=>{t.target.type==="number"&&t.preventDefault()};function CS(){var b;const[t,e]=k.useState(""),[n,r]=k.useState(!1),{state:i,addPurchase:s,getDisplayUnitName:a}=Xe(),{theme:o,currentTheme:l}=me(),u=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],d=g=>Math.round((g+Number.EPSILON)*100)/100,h=g=>{const p=d(g);return p%1===0?p.toString():p.toFixed(2)},f=parseFloat(t)||0,m=i.unitCost||0,x=m>0?d(f/m):0,y=async g=>{g.preventDefault(),r(!0);try{const p=d(parseFloat(t)),v=x;if(isNaN(p)||p<=0){alert("Please enter a valid positive amount");return}if(m<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(v<=0){alert("The calculated units must be greater than 0");return}s(p,v),e(""),alert(`Purchase added successfully! Added ${h(v)} ${a()} for ${i.currencySymbol||"R"}${h(p)}`)}catch(p){console.error("Error adding purchase:",p),alert("Error adding purchase. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:y,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${o.text} mb-3`,children:["💰 Amount (",((b=u.find(g=>g.code===(i.currency||"ZAR")))==null?void 0:b.name)||"South African Rand",")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${o.gradient}`,children:c.jsx(Ye,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currency",value:t,onChange:g=>e(g.target.value),onWheel:Dr,step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 border-4 ${o.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${o.border} ${o.card} ${o.text} ${o.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${o.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:`block text-sm font-semibold ${o.text} mb-3`,children:["⚡ Units Preview (",a(),")"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${o.gradient}`,children:c.jsx(Re,{className:"h-4 w-4 text-white"})})}),c.jsx("div",{className:`w-full pl-12 pr-4 py-4 border-4 ${o.border} rounded-xl ${o.secondary} ${o.text} font-bold text-lg shadow-lg flex items-center min-h-[56px]`,children:f>0&&m>0?c.jsxs("span",{className:`${o.text} font-bold`,children:[h(x)," ",a()]}):c.jsx("span",{className:`${o.textSecondary} font-medium`,children:m<=0?"Set unit cost in Settings first":"Enter amount above to see units"})})]}),c.jsx("p",{className:`mt-2 text-xs ${o.textSecondary} opacity-80 font-medium`,children:"⚡ Live preview of units you'll receive"})]}),c.jsxs("div",{className:`p-6 ${o.card} rounded-xl border ${o.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${o.gradient} mr-3`,children:c.jsx(Rp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${o.text} text-lg`,children:"Calculation Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${o.text} font-bold`,children:[i.currencySymbol||"R",h(m)," per ",a()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Amount:"}),c.jsxs("span",{className:`font-bold text-lg ${o.text}`,children:[i.currencySymbol||"R",h(f)]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Units:"}),c.jsxs("span",{className:`font-bold text-lg ${o.text}`,children:[h(x)," ",a()]})]}),c.jsx("div",{className:`border-t ${o.border} my-3`}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"New Total Units:"}),c.jsxs("span",{className:`font-bold text-xl ${o.text}`,children:[h(d(i.currentUnits+x))," ",a()]})]})]})]}),c.jsx("button",{type:"submit",disabled:n||f<=0||x<=0||m<=0,className:`w-full bg-gradient-to-r ${o.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Adding Purchase..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Ye,{className:"h-5 w-5"}),"Add Purchase"]})})}),c.jsxs("div",{className:`text-center p-4 ${o.card} rounded-xl border ${o.border}`,children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${o.gradient} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${o.text}`,children:"Live Calculator"})]}),c.jsx("p",{className:`text-xs ${o.textSecondary} leading-relaxed`,children:"Enter the amount you want to spend and see the units you'll receive in real-time. The calculation is based on your current unit cost setting."})]})]})}function PS(){const{state:t,getDisplayUnitName:e}=Xe(),{theme:n,currentTheme:r}=me(),i=(o,l="bg-gray-800/50")=>r==="dark"?l:o,s=t.purchases.reduce((o,l)=>o+l.currency,0),a=t.purchases.reduce((o,l)=>o+l.units,0);return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Purchases"}),c.jsx(lo,{title:"How Purchases Work",content:"Enter the amount you want to spend and see exactly how many units you'll receive. The calculation is based on your unit cost setting. All purchases are automatically added to your current units balance and saved to history.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Add new purchases and view your purchase history"})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} ${i("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),"Add New Purchase"]}),c.jsx("div",{className:`${i("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4`,children:c.jsx(CS,{})})]}),c.jsxs("div",{className:"space-y-3",children:[c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Spent"}),c.jsxs("p",{className:`text-lg font-bold ${n.text}`,children:[t.currencySymbol||"R",s.toFixed(2)]})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Re,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsxs("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:["Total ",e()," Purchased"]}),c.jsx("p",{className:`text-lg font-bold ${n.text}`,children:a.toFixed(2)})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:`text-xs ${n.textSecondary} font-medium`,children:e()})})]})}),c.jsx("div",{className:`${n.card} rounded-xl shadow-lg p-4 border ${n.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} shadow-lg`,children:c.jsx(Ca,{className:"h-5 w-5 text-white"})}),c.jsxs("div",{children:[c.jsx("p",{className:`text-sm font-medium ${n.textSecondary} opacity-80`,children:"Total Purchases"}),c.jsx("p",{className:`text-lg font-bold ${n.text}`,children:t.purchases.length})]})]}),c.jsx("div",{className:"text-right",children:c.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})})]})})]})]}),c.jsxs("div",{className:`${n.card} rounded-2xl shadow-lg p-6 border ${n.border} ${i("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${n.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${n.gradient} shadow-md`,children:c.jsx(Ca,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.purchases.slice(0,10).map(o=>c.jsx("div",{className:`p-4 ${i("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${i("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${n.text} text-lg`,children:[t.currencySymbol||"R",o.currency.toFixed(2)]}),c.jsxs("p",{className:`text-sm ${n.textSecondary} opacity-80`,children:[o.units.toFixed(2)," ",e()," @ ",t.currencySymbol||"R",o.unitCost.toFixed(2),"/",e()]}),c.jsx("p",{className:`text-xs ${n.textSecondary} mt-1 opacity-70`,children:o.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200",children:["+",o.units.toFixed(2)," ",e()]}),c.jsxs("p",{className:`text-xs ${n.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",o.currency.toFixed(2)]})]})]})},o.id)),t.purchases.length===0&&c.jsxs("div",{className:`text-center py-12 ${i("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${i("border-white/40","border-gray-600")}`,children:[c.jsx("div",{className:`p-4 rounded-2xl ${n.secondary} w-fit mx-auto mb-4`,children:c.jsx(Ye,{className:`h-12 w-12 ${n.textSecondary}`})}),c.jsx("p",{className:`text-sm ${n.textSecondary} opacity-80 font-medium`,children:"No purchases yet"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-60 mt-1`,children:"Add your first purchase above to get started"})]})]})]})]})]})}function MS(){const[t,e]=k.useState(""),[n,r]=k.useState(!1),{state:i,updateUsage:s,usageSinceLastRecording:a,getDisplayUnitName:o}=Xe(),{theme:l,currentTheme:u}=me(),d=parseFloat(t)||0,h=i.currentUnits-d,f=h*i.unitCost,m=async x=>{x.preventDefault(),r(!0);try{const y=parseFloat(t);if(isNaN(y)||y<0){alert("Please enter a valid meter reading (0 or greater)");return}if(y>i.currentUnits){alert("Current reading cannot be higher than your available units");return}s(y),e(""),alert(`Usage recorded successfully! Used ${h.toFixed(2)} ${o()} costing ${i.currencySymbol||"R"}${f.toFixed(2)}`)}catch(y){console.error("Error recording usage:",y),alert("Error recording usage. Please try again.")}finally{r(!1)}};return c.jsxs("form",{onSubmit:m,className:"space-y-6",children:[c.jsxs("div",{className:`p-5 ${l.card} rounded-xl border ${l.border} shadow-sm`,children:[c.jsxs("h3",{className:`font-semibold ${l.text} mb-4 flex items-center gap-2`,children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient}`,children:c.jsx(Re,{className:"h-4 w-4 text-white"})}),"Current Status"]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Available Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.previousUnits.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-2 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Usage Since Last:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[a.toFixed(2)," ",o()]})]})]})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currentReading",className:`block text-sm font-semibold ${l.text} mb-3`,children:"Current Meter Reading"}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient}`,children:c.jsx(Re,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"currentReading",value:t,onChange:x=>e(x.target.value),onWheel:Dr,step:"0.01",min:"0",max:i.currentUnits,placeholder:"Enter current meter reading",className:`w-full pl-12 pr-4 py-4 border-4 ${l.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${l.border} ${l.card} ${l.text} ${l.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${l.textSecondary} opacity-80 font-medium`,children:"📊 Enter the current reading from your electricity meter"})]}),d>0&&c.jsxs("div",{className:`p-6 ${l.card} rounded-xl border ${l.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${l.gradient} mr-3`,children:c.jsx(Rp,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${l.text} text-lg`,children:"Usage Calculation"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Previous Units:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[i.currentUnits.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"New Reading:"}),c.jsxs("span",{className:`${l.text} font-bold`,children:[d.toFixed(2)," ",o()]})]}),c.jsx("div",{className:`border-t ${l.border} my-3`}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Units Used:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[h.toFixed(2)," ",o()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[i.currencySymbol||"R",f.toFixed(2)]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${l.secondary} rounded-lg border ${l.border}`,children:[c.jsx("span",{className:`${l.textSecondary} font-medium`,children:"Remaining Units:"}),c.jsxs("span",{className:`font-bold text-lg ${l.text}`,children:[d.toFixed(2)," ",o()]})]})]}),h<0&&c.jsx("div",{className:`mt-4 p-4 ${l.secondary} border ${l.border} rounded-xl`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-1 rounded-lg ${l.accent} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"⚠️"})}),c.jsx("span",{className:`${l.textSecondary} text-sm font-medium`,children:"Warning: New reading cannot be higher than available units"})]})})]}),c.jsx("button",{type:"submit",disabled:n||d<=0||d>i.currentUnits,className:`w-full bg-gradient-to-r ${l.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:n?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Recording Usage..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Re,{className:"h-5 w-5"}),"Record Usage"]})})}),c.jsxs("div",{className:`text-center p-4 ${l.card} rounded-xl border ${l.border}`,children:[c.jsxs("div",{className:"flex items-center justify-center mb-2",children:[c.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${l.gradient} mr-2`,children:c.jsx("span",{className:"text-white text-xs",children:"💡"})}),c.jsx("span",{className:`text-sm font-semibold ${l.text}`,children:"How it works"})]}),c.jsx("p",{className:`text-xs ${l.textSecondary} leading-relaxed`,children:"Record your current meter reading to track electricity usage. The system will calculate how many units you've used since the last recording."})]})]})}function ic({children:t,className:e=""}){const{theme:n}=me(),[r,i]=k.useState(!0),[s,a]=k.useState(!1),o=k.useRef(null);k.useEffect(()=>{const u=()=>{if(o.current){const{scrollWidth:d,clientWidth:h}=o.current;a(d>h)}};return u(),window.addEventListener("resize",u),()=>window.removeEventListener("resize",u)},[t]),k.useEffect(()=>{if(!s){i(!1);return}const u=setTimeout(()=>{i(!1)},5e3);return()=>clearTimeout(u)},[s]);const l=()=>{i(!1)};return c.jsxs("div",{className:`relative ${e}`,children:[r&&s&&c.jsx("div",{className:"md:hidden absolute top-2 right-2 z-20 pointer-events-none",children:c.jsxs("div",{className:`flex items-center gap-2 ${n.primary} text-white px-3 py-2 rounded-full shadow-lg backdrop-blur-sm animate-bounce`,children:[c.jsx("span",{className:"text-xs font-medium",children:"Swipe to see more"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse"}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),c.jsx("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]}),c.jsx("span",{className:"text-sm",children:"→"})]})}),s&&c.jsx("div",{className:`md:hidden absolute top-0 right-0 bottom-0 w-8 bg-gradient-to-l ${n.card}/80 to-transparent z-10 pointer-events-none`}),c.jsx("div",{ref:o,className:"overflow-x-auto overflow-y-hidden",style:{WebkitOverflowScrolling:"touch"},onScroll:l,children:t})]})}ns.register(nc,rc,qs,rS,_g,bg);function $S(){const{state:t,usageSinceLastRecording:e,getDisplayUnitName:n,weeklyUsageTotal:r,monthlyUsageTotal:i,weeklyPurchaseTotal:s,monthlyPurchaseTotal:a}=Xe(),{theme:o,currentTheme:l}=me(),u=(y,b="bg-gray-800/50")=>l==="dark"?b:y,d=t.usageHistory.reduce((y,b)=>y+b.usage,0),h=t.usageHistory.length>0?d/t.usageHistory.length:0,f=t.usageHistory.slice(-7).reverse(),m={labels:f.length>0?f.map((y,b)=>new Date(y.timestamp).toLocaleDateString("en-US",{month:"short",day:"numeric"})):["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:`Daily Usage (${n()})`,data:f.length>0?f.map(y=>y.usage):[12.5,15.2,8.7,22.1,18.9,14.3,16.8],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(139, 92, 246, 0.8)","rgba(236, 72, 153, 0.8)","rgba(34, 197, 94, 0.8)","rgba(251, 146, 60, 0.8)","rgba(14, 165, 233, 0.8)","rgba(168, 85, 247, 0.8)"],borderColor:["rgba(99, 102, 241, 1)","rgba(139, 92, 246, 1)","rgba(236, 72, 153, 1)","rgba(34, 197, 94, 1)","rgba(251, 146, 60, 1)","rgba(14, 165, 233, 1)","rgba(168, 85, 247, 1)"],borderWidth:2,borderRadius:8,borderSkipped:!1}]},x={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!0,text:"Daily Usage Trend",font:{size:16,weight:"bold"},color:o.text==="text-gray-900"?"#1f2937":"#f9fafb",padding:20},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff",borderColor:"rgba(255, 255, 255, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(y){return`Usage: ${y.parsed.y.toFixed(2)} ${n()}`}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(156, 163, 175, 0.2)",drawBorder:!1},ticks:{color:o.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12},callback:function(y){return y+" "+n()}}},x:{grid:{display:!1},ticks:{color:o.textSecondary==="text-gray-600"?"#6b7280":"#9ca3af",font:{size:12}}}},animation:{duration:1500,easing:"easeInOutQuart"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${o.text}`,children:"Usage Tracking"}),c.jsx(lo,{title:"Usage Calculation",content:"Enter your current meter reading to track usage. The app calculates: Previous Reading - Current Reading = Usage. For example: if you had 100 units and now have 75 units, you used 25 units. The chart shows your daily usage patterns.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${o.textSecondary}`,children:"Record your current meter readings and track electricity usage"})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-6 border ${o.border} ${u("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${o.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:c.jsx(Re,{className:"h-5 w-5 text-white"})}),"Record New Reading"]}),c.jsx("div",{className:`${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4`,children:c.jsx(MS,{})})]}),c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-6 border ${o.border} ${u("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${o.text} mb-4 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 shadow-md",children:c.jsx(Ca,{className:"h-5 w-5 text-white"})}),"Recent Readings"]}),c.jsxs("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:[t.usageHistory.slice(0,10).map(y=>c.jsx("div",{className:`p-4 ${u("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")} shadow-sm hover:shadow-md transition-all duration-200`,children:c.jsxs("div",{className:"flex justify-between items-start",children:[c.jsxs("div",{children:[c.jsxs("p",{className:`font-semibold ${o.text} text-lg`,children:[y.currentUnits.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-sm ${o.textSecondary} opacity-80`,children:["Previous: ",y.previousUnits.toFixed(2)," ",n()]}),c.jsx("p",{className:`text-xs ${o.textSecondary} mt-1 opacity-70`,children:y.timestamp})]}),c.jsxs("div",{className:"text-right",children:[c.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${y.usage>0?"bg-gradient-to-r from-rose-100 to-pink-100 text-rose-700 border border-rose-200":"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-700 border border-gray-200"}`,children:["-",y.usage.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${o.textSecondary} mt-1 font-medium`,children:[t.currencySymbol||"R",(y.usage*t.unitCost).toFixed(2)]})]})]})},y.id)),t.usageHistory.length===0&&c.jsxs("div",{className:`text-center py-12 ${u("bg-white/40","bg-gray-700/40")} backdrop-blur-sm rounded-xl border ${u("border-white/40","border-gray-600")}`,children:[c.jsx("div",{className:`p-4 rounded-2xl ${o.secondary} w-fit mx-auto mb-4`,children:c.jsx(pt,{className:`h-12 w-12 ${o.textSecondary}`})}),c.jsx("p",{className:`text-sm ${o.textSecondary} opacity-80 font-medium`,children:"No usage records yet"}),c.jsx("p",{className:`text-xs ${o.textSecondary} opacity-60 mt-1`,children:"Record your first reading above to get started"})]})]})]})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.jsxs("div",{className:"space-y-3",children:[c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border}`,children:c.jsxs(ic,{children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-2 min-w-max",children:[c.jsx("h3",{className:`text-base font-semibold ${o.text}`,children:"This Week"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1 rounded-lg ${o.secondary}`,children:c.jsx(Ye,{className:`h-3 w-3 ${o.textSecondary}`})}),c.jsx("div",{className:`p-1 rounded-lg ${o.secondary}`,children:c.jsx(pt,{className:`h-3 w-3 ${o.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-4 min-w-max",children:[c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Purchases"}),c.jsxs("p",{className:`text-base font-bold ${o.text}`,children:[t.currencySymbol,s.toFixed(2)]})]}),c.jsx("div",{className:`border-t sm:border-t-0 sm:border-l ${o.border} h-px sm:h-6 w-full sm:w-px`}),c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Usage"}),c.jsxs("p",{className:`text-base font-bold ${o.text}`,children:[r.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${o.textSecondary}`,children:["Cost: ",t.currencySymbol,(r*t.unitCost).toFixed(2)]})]})]})]})}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border}`,children:c.jsxs(ic,{children:[c.jsxs("div",{className:"flex items-center space-x-2 mb-2 min-w-max",children:[c.jsx("h3",{className:`text-base font-semibold ${o.text}`,children:"This Month"}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`p-1 rounded-lg ${o.secondary}`,children:c.jsx(Ye,{className:`h-3 w-3 ${o.textSecondary}`})}),c.jsx("div",{className:`p-1 rounded-lg ${o.secondary}`,children:c.jsx(pt,{className:`h-3 w-3 ${o.textSecondary}`})})]})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-4 min-w-max",children:[c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Purchases"}),c.jsxs("p",{className:`text-base font-bold ${o.text}`,children:[t.currencySymbol,a.toFixed(2)]})]}),c.jsx("div",{className:`border-t sm:border-t-0 sm:border-l ${o.border} h-px sm:h-6 w-full sm:w-px`}),c.jsxs("div",{className:"text-center sm:text-right",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Usage"}),c.jsxs("p",{className:`text-base font-bold ${o.text}`,children:[i.toFixed(2)," ",n()]}),c.jsxs("p",{className:`text-xs ${o.textSecondary}`,children:["Cost: ",t.currencySymbol,(i*t.unitCost).toFixed(2)]})]})]})]})})]}),c.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg`,children:c.jsx(Re,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Current Reading"}),c.jsx("p",{className:`text-lg font-bold ${o.text}`,children:t.currentUnits.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg`,children:c.jsx(pt,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Usage Since Last"}),c.jsx("p",{className:`text-lg font-bold ${o.text}`,children:e.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg`,children:c.jsx(Ca,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Total Usage"}),c.jsx("p",{className:`text-lg font-bold ${o.text}`,children:d.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary} font-medium`,children:n()})]})]})}),c.jsx("div",{className:`${o.card} rounded-xl shadow-lg p-3 border ${o.border} hover:shadow-xl transition-all duration-300`,children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${o.gradient} shadow-lg`,children:c.jsx(Ad,{className:"h-4 w-4 text-white"})}),c.jsxs("div",{className:"ml-3",children:[c.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Average Usage"}),c.jsx("p",{className:`text-lg font-bold ${o.text}`,children:h.toFixed(2)}),c.jsx("p",{className:`text-xs ${o.textSecondary} font-medium`,children:n()})]})]})})]})]}),c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-8 border ${o.border} ${u("bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50","bg-gray-800/50")}`,children:[c.jsx("div",{className:"flex items-center justify-between mb-6",children:c.jsxs("div",{children:[c.jsxs("h2",{className:`text-2xl font-bold ${o.text} flex items-center gap-3`,children:[c.jsx("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg",children:c.jsx(Jc,{className:"h-6 w-6 text-white"})}),"Usage Analytics"]}),c.jsx("p",{className:`mt-2 ${o.textSecondary} opacity-80`,children:"Visual representation of your daily electricity consumption"})]})}),c.jsxs("div",{className:"h-80 relative",children:[c.jsx("div",{className:`absolute inset-0 ${u("bg-gradient-to-br from-white/80 to-white/40","bg-gray-700/40")} rounded-xl backdrop-blur-sm`}),c.jsx("div",{className:"relative h-full p-4",children:c.jsx(_S,{data:m,options:x})})]})]}),c.jsxs("div",{className:`${o.card} rounded-2xl shadow-lg p-8 border ${o.border} ${u("bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50","bg-gray-800/50")}`,children:[c.jsxs("h2",{className:`text-xl font-semibold ${o.text} mb-6 flex items-center gap-3`,children:[c.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-amber-500 to-orange-600 shadow-md",children:c.jsx(Ad,{className:"h-5 w-5 text-white"})}),"How Usage is Calculated"]}),c.jsx("div",{className:`p-6 ${o.card} rounded-xl border ${o.border} shadow-sm`,children:c.jsxs("div",{className:"space-y-4 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Previous Reading:"}),c.jsxs("span",{className:`${o.text} font-bold text-lg`,children:[t.previousUnits.toFixed(2)," ",n()]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Current Reading:"}),c.jsxs("span",{className:`${o.text} font-bold text-lg`,children:[t.currentUnits.toFixed(2)," ",n()]})]}),c.jsx("div",{className:`border-t ${o.border} my-4`}),c.jsxs("div",{className:`flex justify-between items-center p-4 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.text} font-semibold`,children:"Usage Since Last Recording:"}),c.jsxs("div",{className:"text-right",children:[c.jsxs("div",{className:`${o.textSecondary} text-sm mb-1`,children:[t.previousUnits.toFixed(2)," - ",t.currentUnits.toFixed(2)]}),c.jsxs("span",{className:`${o.text} font-bold text-xl`,children:[e.toFixed(2)," ",n()]})]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${o.secondary} rounded-lg border ${o.border}`,children:[c.jsx("span",{className:`${o.textSecondary} font-medium`,children:"Cost of Usage:"}),c.jsxs("span",{className:`${o.text} font-bold text-lg`,children:[t.currencySymbol||"R",(e*t.unitCost).toFixed(2)]})]})]})})]})]})}function ES({history:t}){const{state:e,getDisplayUnitName:n}=Xe(),{theme:r,currentTheme:i}=me();return t.length===0?null:c.jsx(ic,{children:c.jsxs("table",{className:`min-w-full divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,style:{minWidth:"600px"},children:[c.jsx("thead",{className:r.secondary,children:c.jsxs("tr",{children:[c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Type"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Details"}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:n()}),c.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${r.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),c.jsx("tbody",{className:`${r.card} divide-y ${i==="dark"?"divide-gray-600":"divide-gray-200"}`,children:t.map(s=>c.jsxs("tr",{className:`${i==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("div",{className:"flex items-center",children:s.type==="purchase"?c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${r.secondary}`,children:c.jsx(Ye,{className:`h-4 w-4 ${r.textSecondary}`})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800"}`,children:[c.jsx(S1,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:`p-2 rounded-lg ${i==="dark"?"bg-red-900/30":"bg-red-100"}`,children:c.jsx(pt,{className:"h-4 w-4 text-red-600"})}),c.jsx("div",{className:"ml-3",children:c.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${i==="dark"?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800"}`,children:[c.jsx(w1,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.timestamp}),c.jsx("td",{className:`px-6 py-4 text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["@ ",e.currencySymbol||"R",s.unitCost.toFixed(2)," per ",n()]})]}):c.jsxs("div",{children:[c.jsx("p",{className:"font-medium",children:"Usage Recording"}),c.jsxs("p",{className:`text-xs ${r.textSecondary}`,children:["From ",s.previousUnits.toFixed(2)," to ",s.currentUnits.toFixed(2)," ",n()]})]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",s.units.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",s.usage.toFixed(2)]})}),c.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm ${r.text}`,children:s.type==="purchase"?c.jsxs("span",{className:`${r.text} font-medium`,children:["+",e.currencySymbol||"R",s.currency.toFixed(2)]}):c.jsxs("span",{className:`${r.text} font-medium`,children:["-",e.currencySymbol||"R",(s.usage*e.unitCost).toFixed(2)]})})]},`${s.type}-${s.id}`))})]})})}function TS(){const t=Ar(),[e,n]=k.useState("all"),[r,i]=k.useState(""),[s,a]=k.useState(!1),[o,l]=k.useState(""),[u,d]=k.useState(""),[h,f]=k.useState(new Date),m=k.useRef(null),{state:x,getDisplayUnitName:y,weeklyPurchaseTotal:b,monthlyPurchaseTotal:g,weeklyUsageTotal:p,monthlyUsageTotal:v}=Xe(),{theme:w,currentTheme:_}=me(),N=[...x.purchases.map(R=>({...R,type:"purchase"})),...x.usageHistory.map(R=>({...R,type:"usage"}))].sort((R,V)=>new Date(V.date)-new Date(R.date)).filter(R=>{const V=e==="all"||R.type===e;if(r&&!o&&!u)return V&&R.date.includes(r);if(o||u){const I=new Date(R.date),U=o?new Date(o):null,T=u?new Date(u):null;let P=!0;return U&&(P=P&&I>=U),T&&(P=P&&I<=T),V&&P}return V});x.purchases.reduce((R,V)=>R+V.currency,0),x.usageHistory.reduce((R,V)=>R+V.usage,0)*x.unitCost;const $=[{id:"all",name:"All Activity",icon:Pa},{id:"purchase",name:"Purchases",icon:Ye},{id:"usage",name:"Usage",icon:pt}],M=()=>{i(""),a(!1)},E=()=>{i(""),l(""),d(""),a(!1)},L=r||o||u;return k.useEffect(()=>{const R=setInterval(()=>{f(new Date)},1e3);return()=>clearInterval(R)},[]),k.useEffect(()=>{const R=V=>{m.current&&!m.current.contains(V.target)&&a(!1)};if(s)return document.addEventListener("mousedown",R),()=>document.removeEventListener("mousedown",R)},[s]),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h1",{className:`text-3xl font-bold ${w.text}`,children:"History"}),c.jsx("p",{className:`mt-2 ${w.textSecondary}`,children:"View detailed logs of all purchases and usage patterns"})]}),c.jsxs("div",{className:`${w.card} rounded-2xl shadow-lg border ${w.border}`,children:[c.jsx("div",{className:`p-4 md:p-8 ${w.secondary} rounded-t-2xl`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("div",{className:"flex flex-wrap gap-2",children:$.map(R=>c.jsxs("button",{onClick:()=>n(R.id),className:`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${e===R.id?`${w.primary} text-white`:`${w.text} hover:${w.secondary}`}`,children:[c.jsx(R.icon,{className:"mr-2 h-4 w-4"}),R.name]},R.id))}),c.jsxs("div",{ref:m,className:"space-y-3",children:[c.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[c.jsx(N1,{className:`h-5 w-5 ${w.textSecondary}`}),c.jsx("span",{className:`text-sm font-medium ${w.text}`,children:"Filter by Date"})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[c.jsx("input",{type:"date",value:r||new Date().toISOString().split("T")[0],onChange:R=>{i(R.target.value),R.target.value&&(l(""),d(""))},className:`px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm`,placeholder:"Filter by date"}),c.jsx("button",{onClick:()=>a(!s),className:`px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} hover:${w.secondary} transition-colors text-sm ${o||u?"ring-2 ring-blue-500":""}`,children:"📅 Range"}),L&&c.jsx("button",{onClick:E,className:`px-3 py-2 text-sm ${w.textSecondary} hover:${w.text} transition-colors border ${w.border} rounded-lg`,children:"Clear All"})]}),s&&c.jsx("div",{className:`mt-3 p-4 ${w.card} border ${w.border} rounded-xl shadow-xl w-full max-w-md`,children:c.jsxs("div",{className:"space-y-4",children:[c.jsx("h3",{className:`font-semibold ${w.text} text-sm`,children:"Select Date Range"}),c.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${w.textSecondary} mb-1`,children:"From Date"}),c.jsx("input",{type:"date",value:o,onChange:R=>{l(R.target.value),i("")},className:`w-full px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm`})]}),c.jsxs("div",{children:[c.jsx("label",{className:`block text-xs font-medium ${w.textSecondary} mb-1`,children:"To Date"}),c.jsx("input",{type:"date",value:u,onChange:R=>{d(R.target.value),i("")},className:`w-full px-3 py-2 border ${w.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${w.card} ${w.text} text-sm`})]})]}),c.jsxs("div",{className:"flex justify-between items-center pt-2",children:[c.jsx("button",{onClick:()=>{l(""),d("")},className:`text-xs ${w.textSecondary} hover:${w.text} transition-colors`,children:"Clear Range"}),c.jsx("button",{onClick:M,className:`px-4 py-2 bg-gradient-to-r ${w.gradient} text-white rounded-lg hover:opacity-90 transition-colors text-sm font-medium`,children:"Apply"})]})]})})]})]})}),c.jsx("div",{className:`border-t ${w.border}`,children:c.jsx(ES,{history:N})})]}),N.length===0&&c.jsxs("div",{className:`${w.card} rounded-2xl shadow-lg p-16 text-center border ${w.border}`,children:[c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:`absolute inset-0 ${w.secondary} rounded-full opacity-20 scale-110`}),c.jsx("div",{className:`relative p-6 rounded-2xl ${w.secondary} w-fit mx-auto`,children:c.jsx(Pa,{className:`h-16 w-16 ${w.textSecondary}`})})]}),c.jsx("h3",{className:`mt-6 text-2xl font-bold ${w.text}`,children:"No history found"}),c.jsx("p",{className:`mt-3 ${w.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:L?"No records found for the selected date range. Try adjusting your filters or clear them to see all records.":"Start by making purchases or recording usage to see your history here."}),!L&&c.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[c.jsx("button",{onClick:()=>t("/purchases"),className:`bg-gradient-to-r ${w.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"💰"}),"Add Purchase"]})}),c.jsx("button",{onClick:()=>t("/usage"),className:`bg-gradient-to-r ${w.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsxs("div",{className:"flex items-center gap-2",children:[c.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}function RS(){const[t,e]=k.useState(!1),n=k.useRef(null),{currentTheme:r,setCurrentTheme:i,theme:s}=me(),a=o=>o?`${s.border} ring-2 ring-opacity-50`:r==="dark"?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300";return k.useEffect(()=>{function o(l){n.current&&!n.current.contains(l.target)&&e(!1)}return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]),c.jsxs("div",{className:"space-y-8",children:[c.jsxs("div",{children:[c.jsxs("h3",{className:`text-lg font-semibold ${s.text} mb-4 flex items-center`,children:[c.jsx(Dp,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),c.jsxs("div",{className:"relative",ref:n,children:[c.jsxs("button",{onClick:()=>e(!t),className:`w-full p-4 ${s.card} border ${s.border} rounded-lg flex items-center justify-between hover:${s.secondary} transition-colors`,children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2",children:[c.jsx("div",{className:`h-6 w-16 ${mt[r].gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-2 w-2 ${mt[r].primary} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[r].accent} rounded`}),c.jsx("div",{className:`h-2 w-2 ${mt[r].secondary} rounded`})]})]}),c.jsx("span",{className:`text-lg font-medium ${s.text}`,children:mt[r].name})]}),c.jsx(Kl,{className:`h-5 w-5 ${s.textSecondary} transition-transform ${t?"rotate-180":""}`})]}),t&&c.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${s.card} border ${s.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:c.jsx("div",{className:"grid grid-cols-1 gap-2 p-2",children:Object.entries(mt).map(([o,l])=>c.jsx("button",{onClick:()=>{i(o),e(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left ${a(r===o)}`,children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[c.jsx("div",{className:`h-8 w-20 ${l.gradient} bg-gradient-to-r rounded`}),c.jsxs("div",{className:"flex space-x-1",children:[c.jsx("div",{className:`h-3 w-3 ${l.primary} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.accent} rounded`}),c.jsx("div",{className:`h-3 w-3 ${l.secondary} rounded`})]})]}),c.jsx("div",{className:"flex-1",children:c.jsx("p",{className:`text-sm font-medium ${s.text}`,children:l.name})}),r===o&&c.jsx("div",{className:"flex-shrink-0",children:c.jsx(_1,{className:`h-5 w-5 ${s.text}`})})]})},o))})})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Preview"}),c.jsxs("div",{className:`p-6 ${s.card} rounded-lg border ${s.border} space-y-4`,children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("h4",{className:`text-2xl font-bold ${s.text}`,children:"Sample Dashboard"}),c.jsx("div",{className:`px-3 py-1 ${s.primary} text-white rounded-full text-base`,children:"Active"})]}),c.jsx("div",{className:"grid grid-cols-1 gap-4",children:c.jsxs("div",{className:`p-4 ${s.card} border ${s.border} rounded-lg shadow-sm`,children:[c.jsx("p",{className:`text-base ${s.textSecondary}`,children:"Current Units"}),c.jsx("p",{className:`text-2xl font-bold ${s.text}`,children:"125.50"})]})}),c.jsxs("div",{className:"flex space-x-2",children:[c.jsx("button",{className:`px-4 py-2 ${s.primary} text-white rounded-lg text-base`,children:"Primary Button"}),c.jsx("button",{className:`px-4 py-2 border ${s.border} ${s.text} rounded-lg text-base`,children:"Secondary Button"})]})]})]}),c.jsxs("div",{children:[c.jsx("h3",{className:`text-lg font-semibold ${s.text} mb-4`,children:"Reset Appearance"}),c.jsx("div",{className:"space-y-3",children:c.jsx("button",{onClick:()=>{i("electric"),e(!1)},className:`w-full px-6 py-3 bg-gradient-to-r ${s.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`,children:"Reset to Default Theme"})})]})]})}function DS(){const[t,e]=k.useState(!1),[n,r]=k.useState(!1),[i,s]=k.useState(!1),{state:a,factoryReset:o,dashboardReset:l}=Xe(),{theme:u,currentTheme:d}=me(),h=(x,y="bg-gray-800/50")=>d==="dark"?y:x,f=async()=>{s(!0);try{o(),e(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(x){console.error("Error during factory reset:",x),alert("Error during factory reset. Please try again.")}finally{s(!1)}},m=async()=>{s(!0);try{l(),r(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(x){console.error("Error during dashboard reset:",x),alert("Error during dashboard reset. Please try again.")}finally{s(!1)}};return c.jsxs("div",{className:"space-y-8",children:[c.jsx("div",{className:`p-6 border ${u.border} rounded-lg ${h("bg-white","bg-gray-800/50")}`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(Lp,{className:"h-6 w-6 text-orange-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text}`,children:"Dashboard Data Reset"}),c.jsx("p",{className:`mt-2 text-sm ${u.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),c.jsxs("div",{className:`mt-4 p-3 ${h("bg-orange-50 border-orange-200","bg-orange-900/20 border-orange-700")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be reset:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsx("li",{children:"• Current units will be set to 0"}),c.jsx("li",{children:"• Previous units will be set to 0"}),c.jsx("li",{children:"• Usage since last recording will be reset"})]}),c.jsx("h4",{className:`text-sm font-medium mt-3 mb-2 ${d==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be preserved:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-orange-200":"text-orange-700"}`,children:[c.jsx("li",{children:"• All purchase history"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]})]}),n?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-3 ${h("bg-red-50 border-red-200","bg-red-900/20 border-red-700")} border rounded-lg`,children:[c.jsx(wr,{className:"h-5 w-5 text-red-600 mr-2"}),c.jsx("span",{className:`text-sm ${d==="dark"?"text-red-200":"text-red-800"}`,children:"Are you sure? This action cannot be undone."})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:m,disabled:i,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Reset Dashboard"}),c.jsx("button",{onClick:()=>r(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>r(!0),className:"mt-4 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})]})}),c.jsx("div",{className:`p-6 border ${h("border-red-200 bg-red-50","border-red-700 bg-red-900/20")} rounded-lg`,children:c.jsxs("div",{className:"flex items-start",children:[c.jsx("div",{className:"flex-shrink-0",children:c.jsx(M1,{className:"h-6 w-6 text-red-600"})}),c.jsxs("div",{className:"ml-4 flex-1",children:[c.jsx("h3",{className:`text-lg font-semibold ${d==="dark"?"text-red-300":"text-red-800"}`,children:"Factory Reset"}),c.jsx("p",{className:`mt-2 text-sm ${d==="dark"?"text-red-200":"text-red-700"}`,children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),c.jsxs("div",{className:`mt-4 p-3 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx("h4",{className:`text-sm font-medium mb-2 ${d==="dark"?"text-red-300":"text-red-800"}`,children:"What will be deleted:"}),c.jsxs("ul",{className:`text-xs space-y-1 ${d==="dark"?"text-red-200":"text-red-700"}`,children:[c.jsx("li",{children:"• All purchase records"}),c.jsx("li",{children:"• All usage history"}),c.jsx("li",{children:"• Current and previous unit readings"}),c.jsx("li",{children:"• All settings and preferences"}),c.jsx("li",{children:"• Theme and appearance settings"})]}),c.jsxs("div",{className:`mt-3 p-2 ${h("bg-red-200 border-red-400","bg-red-900/40 border-red-500")} border rounded text-xs ${d==="dark"?"text-red-200":"text-red-800"}`,children:[c.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),t?c.jsxs("div",{className:"mt-4 space-y-3",children:[c.jsxs("div",{className:`flex items-center p-3 ${h("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[c.jsx(wr,{className:"h-5 w-5 text-red-700 mr-2"}),c.jsx("span",{className:`text-sm font-medium ${d==="dark"?"text-red-200":"text-red-800"}`,children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),c.jsxs("div",{className:"flex space-x-3",children:[c.jsx("button",{onClick:f,disabled:i,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:i?"Resetting...":"Yes, Delete Everything"}),c.jsx("button",{onClick:()=>e(!1),className:`px-4 py-2 border ${u.border} ${u.text} rounded-lg hover:${u.secondary} transition-colors`,children:"Cancel"})]})]}):c.jsx("button",{onClick:()=>e(!0),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})]})}),c.jsxs("div",{className:`p-6 ${u.card} border ${u.border} rounded-lg`,children:[c.jsx("h3",{className:`text-lg font-semibold ${u.text} mb-4`,children:"Current Data Summary"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"App Data"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Current Units: ",a.currentUnits.toFixed(2)]}),c.jsxs("li",{children:["• Previous Units: ",a.previousUnits.toFixed(2)]}),c.jsxs("li",{children:["• Unit Cost: R",a.unitCost.toFixed(2)]}),c.jsxs("li",{children:["• Threshold: ",a.thresholdLimit.toFixed(2)," units"]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:`font-medium ${u.text} mb-2`,children:"History"}),c.jsxs("ul",{className:`space-y-1 ${u.textSecondary}`,children:[c.jsxs("li",{children:["• Purchases: ",a.purchases.length," records"]}),c.jsxs("li",{children:["• Usage Records: ",a.usageHistory.length," records"]}),c.jsxs("li",{children:["• Last Reset: ",a.lastResetDate?new Date(a.lastResetDate).toLocaleDateString():"Never"]}),c.jsxs("li",{children:["• App Initialized: ",a.isInitialized?"Yes":"No"]})]})]})]})]})]})}function OS(){var I,U;const{state:t,updateSettings:e}=Xe(),{theme:n,currentTheme:r}=me(),[i]=lv(),s=(T,P="bg-gray-800/50")=>r==="dark"?P:T,[a,o]=k.useState(t.unitCost.toString()),[l,u]=k.useState(t.thresholdLimit.toString()),[d,h]=k.useState(t.currency||"ZAR"),[f,m]=k.useState(t.customCurrencyName||""),[x,y]=k.useState(t.customCurrencySymbol||""),[b,g]=k.useState(t.unitName||"kWh"),[p,v]=k.useState(t.customUnitName||""),[w,_]=k.useState(t.notificationsEnabled||!1),[S,N]=k.useState(t.notificationTime||"18:00"),[j,$]=k.useState(!1),M=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CUSTOM",name:"Custom Currency",symbol:"C"}],E=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],L=async T=>{T.preventDefault(),$(!0);try{const P=parseFloat(a),O=parseFloat(l);if(isNaN(P)||P<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(O)||O<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(b==="custom"&&!p.trim()){alert("Please enter a custom unit name");return}if(d==="CUSTOM"&&(!f.trim()||!x.trim())){alert("Please enter both custom currency name and symbol");return}const z=M.find(G=>G.code===d),K=d==="CUSTOM"?x:(z==null?void 0:z.symbol)||"R";e({unitCost:P,thresholdLimit:O,currency:d,currencySymbol:K,customCurrencyName:d==="CUSTOM"?f.trim():"",customCurrencySymbol:d==="CUSTOM"?x.trim():"",unitName:b,customUnitName:b==="custom"?p.trim():"",notificationsEnabled:w,notificationTime:S}),alert("Settings saved successfully!")}catch(P){console.error("Error saving settings:",P),alert("Error saving settings. Please try again.")}finally{$(!1)}},[R,V]=k.useState("general");return k.useEffect(()=>{const T=i.get("section");T&&["general","appearance","reset"].includes(T)&&V(T)},[i]),c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("h1",{className:`text-3xl font-bold ${n.text}`,children:"Settings"}),c.jsx(lo,{title:"Settings Guide",content:"Configure your unit cost (how much you pay per unit), set low units warning threshold, choose your currency and unit names, customize appearance, and set up daily notifications. All settings are saved automatically.",position:"bottom"})]}),c.jsx("p",{className:`mt-2 ${n.textSecondary}`,children:"Configure your app preferences and manage your data"})]}),c.jsx("div",{className:`${n.card} rounded-2xl shadow-lg border ${n.border}`,children:c.jsxs("div",{className:"p-6",children:[R==="general"&&c.jsx("div",{className:"space-y-6",children:c.jsxs("form",{onSubmit:L,className:"space-y-6",children:[c.jsxs("div",{className:`p-6 ${n.card} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${n.gradient} mr-3`,children:c.jsx(k1,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Currency Settings"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${n.text} mb-3`,children:"💰 Currency"}),c.jsx("select",{id:"currency",value:d,onChange:T=>h(T.target.value),className:`w-full px-4 py-4 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,children:M.map(T=>c.jsxs("option",{value:T.code,children:[T.symbol," - ",T.name," (",T.code,")"]},T.code))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"}),d==="CUSTOM"&&c.jsxs("div",{className:"mt-4 space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencyName",className:`block text-sm font-semibold ${n.text} mb-2`,children:"🏷️ Custom Currency Name"}),c.jsx("input",{type:"text",id:"customCurrencyName",value:f,onChange:T=>m(T.target.value),placeholder:"Enter currency name (e.g., Bitcoin, Credits, Points)",className:`w-full px-4 py-3 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} ${n.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:d==="CUSTOM"})]}),c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customCurrencySymbol",className:`block text-sm font-semibold ${n.text} mb-2`,children:"💰 Custom Currency Symbol"}),c.jsx("input",{type:"text",id:"customCurrencySymbol",value:x,onChange:T=>y(T.target.value),placeholder:"Enter symbol (e.g., ₿, Cr, Pts)",maxLength:"5",className:`w-full px-4 py-3 border-4 ${n.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${n.border} ${n.card} ${n.text} ${n.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200`,required:d==="CUSTOM"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['💰 This symbol will be displayed with all amounts (e.g., "',x||"Cr",'100.00")']})]})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-blue-50 to-indigo-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:c.jsx(Re,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Unit Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⚡ Unit Name"}),c.jsx("select",{id:"unitName",value:b,onChange:T=>g(T.target.value),className:`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400`,children:E.map(T=>c.jsx("option",{value:T.value,children:T.label},T.value))}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),b==="custom"&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${n.text} mb-3`,children:"🎯 Custom Unit Name"}),c.jsx("input",{type:"text",id:"customUnitName",value:p,onChange:T=>v(T.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400`,required:b==="custom"}),c.jsxs("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',p||"YourUnit",'")']})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-violet-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:c.jsx(Ye,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Cost Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${n.text} mb-3`,children:["💵 Cost per ",b==="custom"?p||"Unit":b]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:c.jsx(Ye,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"unitCost",value:a,onChange:T=>o(T.target.value),onWheel:Dr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-amber-50 to-yellow-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:c.jsx(wr,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Alert Settings"})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${n.text} mb-3`,children:["⚠️ Low ",b==="custom"?p||"Units":b," Warning Threshold"]}),c.jsxs("div",{className:"relative",children:[c.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:c.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:c.jsx(wr,{className:"h-4 w-4 text-white"})})}),c.jsx("input",{type:"number",id:"thresholdLimit",value:l,onChange:T=>u(T.target.value),onWheel:Dr,step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 ${n.card} ${n.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400`,required:!0})]}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-slate-50 to-gray-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:c.jsx(Bi,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Current Settings Preview"})]}),c.jsxs("div",{className:"space-y-3 text-sm",children:[c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Currency:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.currency==="CUSTOM"?`${t.customCurrencySymbol||"C"} - ${t.customCurrencyName||"Custom Currency"}`:`${((I=M.find(T=>T.code===(t.currency||"ZAR")))==null?void 0:I.symbol)||"R"} - ${((U=M.find(T=>T.code===(t.currency||"ZAR")))==null?void 0:U.name)||"South African Rand"}`})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Name:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Unit Cost:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.currencySymbol||"R",t.unitCost.toFixed(2)," per ",t.unitName==="custom"?t.customUnitName||"Unit":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Low Units Warning:"}),c.jsxs("span",{className:`${n.text} font-bold`,children:[t.thresholdLimit.toFixed(2)," ",t.unitName==="custom"?t.customUnitName||"Units":t.unitName||"kWh"]})]}),c.jsxs("div",{className:`flex justify-between items-center p-3 ${s("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[c.jsx("span",{className:`${n.textSecondary} font-medium`,children:"Last Reset:"}),c.jsx("span",{className:`${n.text} font-bold`,children:t.lastResetDate?new Date(t.lastResetDate).toLocaleDateString():"Never"})]})]})]}),c.jsxs("div",{className:`p-6 ${s("bg-gradient-to-br from-indigo-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${n.border} shadow-sm`,children:[c.jsxs("div",{className:"flex items-center mb-4",children:[c.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3",children:c.jsx(wr,{className:"h-5 w-5 text-white"})}),c.jsx("h3",{className:`font-semibold ${n.text} text-lg`,children:"Notification Settings"})]}),c.jsxs("div",{className:"space-y-4",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("label",{className:`text-sm font-semibold ${n.text}`,children:"🔔 Daily Usage Reminders"}),c.jsx("p",{className:`text-xs ${n.textSecondary} opacity-80 mt-1`,children:"Get reminded to record your electricity usage every day"})]}),c.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[c.jsx("input",{type:"checkbox",checked:w,onChange:T=>_(T.target.checked),className:"sr-only peer","aria-label":"Enable daily usage reminder notifications"}),c.jsx("div",{className:`w-11 h-6 ${r==="dark"?"bg-gray-600":"bg-gray-200"} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600`})]})]}),w&&c.jsxs("div",{children:[c.jsx("label",{htmlFor:"notificationTime",className:`block text-sm font-semibold ${n.text} mb-3`,children:"⏰ Reminder Time"}),c.jsx("input",{type:"time",id:"notificationTime",value:S,onChange:T=>N(T.target.value),className:`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 ${n.card} ${n.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400`}),c.jsx("p",{className:`mt-2 text-xs ${n.textSecondary} opacity-80 font-medium`,children:"⏰ You'll receive a notification at this time every day"})]})]})]}),c.jsx("button",{type:"submit",disabled:j,className:`w-full bg-gradient-to-r ${n.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:c.jsx("div",{className:"flex items-center justify-center gap-2",children:j?c.jsxs(c.Fragment,{children:[c.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):c.jsxs(c.Fragment,{children:[c.jsx(Bi,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),R==="appearance"&&c.jsx("div",{className:"space-y-6",children:c.jsx(RS,{})}),R==="reset"&&c.jsx("div",{className:"space-y-6",children:c.jsx(DS,{})})]})})]})}function LS(){const[t,e]=k.useState(""),[n,r]=k.useState(""),[i,s]=k.useState(""),{initializeApp:a,state:o}=Xe(),{theme:l}=me(),u=d=>{d.preventDefault(),s("");const h=parseFloat(t),f=parseFloat(n);if(isNaN(h)||h<0){s("Please enter a valid number of units (0 or greater)");return}if(isNaN(f)||f<=0){s("Please enter a valid cost per unit (greater than 0)");return}a(h,f)};return c.jsx("div",{className:`min-h-dvh flex items-center justify-center px-4 py-8 ${l.background}`,style:{paddingTop:"calc(env(safe-area-inset-top, 0px) + 2rem)",paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 2rem)"},children:c.jsxs("div",{className:`max-w-lg w-full ${l.card} rounded-2xl shadow-2xl p-8 border ${l.border}`,children:[c.jsxs("div",{className:"text-center mb-8",children:[c.jsx("div",{className:"flex justify-center mb-6",children:c.jsxs("div",{className:"h-24 w-24 flex items-center justify-center",children:[c.jsx("img",{src:"/oie_transparent (1).png",alt:"Prepaid User Electricity Logo",className:"h-24 w-24 object-contain",onError:d=>{d.target.style.display="none",d.target.nextElementSibling.style.display="flex"}}),c.jsx("div",{className:"h-24 w-24 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white",style:{display:"none"},children:c.jsx("span",{className:"text-white text-4xl font-bold",children:"⚡"})})]})}),c.jsx("h1",{className:`text-3xl font-bold ${l.text} mb-4`,children:"Welcome to Prepaid Meter App"}),c.jsx("p",{className:`${l.textSecondary} text-lg mb-2`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),c.jsxs("form",{onSubmit:u,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${l.text} mb-2`,children:"Initial Unit Value"}),c.jsx("input",{type:"number",id:"initialUnits",value:t,onChange:d=>e(d.target.value),onWheel:Dr,step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${l.text} mb-2`,children:["Cost per Unit (",o.currencySymbol||"R",")"]}),c.jsx("input",{type:"number",id:"unitCost",value:n,onChange:d=>r(d.target.value),onWheel:Dr,step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-4 py-4 border-2 ${l.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l.card} ${l.text} text-lg transition-all duration-200 hover:border-blue-300`,required:!0})]}),i&&c.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:c.jsx("p",{className:"text-sm text-red-600",children:i})}),c.jsxs("div",{className:`p-4 ${l.secondary} rounded-lg`,children:[c.jsx("h3",{className:`font-medium ${l.text} mb-2`,children:"What happens next?"}),c.jsxs("ul",{className:`text-sm ${l.textSecondary} space-y-1`,children:[c.jsx("li",{children:"• This will be your starting point for tracking usage"}),c.jsx("li",{children:"• The cost setting will be used for purchase calculations"}),c.jsx("li",{children:"• You can add purchases to increase your units"}),c.jsx("li",{children:"• Track your daily electricity consumption"}),c.jsx("li",{children:"• Set up warnings and monthly resets"})]})]}),c.jsx("button",{type:"submit",className:`w-full bg-gradient-to-r ${l.gradient} text-white py-4 px-6 rounded-xl font-semibold text-lg hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`,children:"🚀 Initialize App"})]}),c.jsx("div",{className:"mt-6 text-center",children:c.jsx("p",{className:`text-xs ${l.textSecondary}`,children:"You can always change these values later in Settings"})})]})})}function zS(){const[t,e]=k.useState(!1),{state:n}=Xe(),{theme:r}=me();return k.useEffect(()=>{const i=s=>{s.target.type==="number"&&document.activeElement===s.target&&s.preventDefault()};return document.addEventListener("wheel",i,{passive:!1}),()=>{document.removeEventListener("wheel",i)}},[]),n.isInitialized?c.jsxs("div",{className:`flex h-dvh ${r.background}`,style:{height:"100dvh"},children:[c.jsx(R1,{isOpen:t,onClose:()=>e(!1)}),c.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[c.jsx("div",{className:"safe-top mobile-safe-top mobile-header",style:{paddingTop:"calc(env(safe-area-inset-top, 0px) + 1rem)"},children:c.jsx(T1,{onMenuClick:()=>e(!0)})}),c.jsx("main",{className:"flex-1 overflow-y-auto px-4 pt-2",style:{paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 5rem)"},children:c.jsx("div",{className:"max-w-7xl mx-auto",children:c.jsxs(Xy,{children:[c.jsx(Tn,{path:"/",element:c.jsx(Jh,{})}),c.jsx(Tn,{path:"/dashboard",element:c.jsx(Jh,{})}),c.jsx(Tn,{path:"/purchases",element:c.jsx(PS,{})}),c.jsx(Tn,{path:"/usage",element:c.jsx($S,{})}),c.jsx(Tn,{path:"/history",element:c.jsx(TS,{})}),c.jsx(Tn,{path:"/settings",element:c.jsx(OS,{})})]})})})]}),c.jsx("div",{className:"fixed bottom-0 left-0 right-0 z-50",style:{paddingBottom:"env(safe-area-inset-bottom, 0px)"},children:c.jsx(D1,{})}),t&&c.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>e(!1)})]}):c.jsx(LS,{})}function FS(){return c.jsx(nv,{children:c.jsx(y1,{children:c.jsx(x1,{children:c.jsx(zS,{})})})})}Go.createRoot(document.getElementById("root")).render(c.jsx(gt.StrictMode,{children:c.jsx(FS,{})}));
