import React, { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import Header from './Header'
import Sidebar from './Sidebar'
import MobileFooterNav from './MobileFooterNav'
import Dashboard from '../Dashboard/Dashboard'
import Purchases from '../Purchases/Purchases'
import Usage from '../Usage/Usage'
import History from '../History/History'
import Settings from '../Settings/Settings'
import InitialSetup from '../Common/InitialSetup'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'

function Layout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { state } = useApp()
  const { theme } = useTheme()

  // Prevent scroll wheel from changing number input values
  useEffect(() => {
    const preventNumberInputScroll = (e) => {
      // Check if the target is a number input and it's focused
      if (e.target.type === 'number' && document.activeElement === e.target) {
        e.preventDefault()
      }
    }

    // Add event listener to prevent scroll on number inputs
    document.addEventListener('wheel', preventNumberInputScroll, { passive: false })

    // Cleanup function
    return () => {
      document.removeEventListener('wheel', preventNumberInputScroll)
    }
  }, [])

  // Show initial setup if app is not initialized
  if (!state.isInitialized) {
    return <InitialSetup />
  }

  return (
    <div className={`flex h-dvh ${theme.background}`} style={{height: '100dvh'}}>
      {/* Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header with proper safe area padding */}
        <div className="safe-top mobile-safe-top mobile-header" style={{paddingTop: 'calc(env(safe-area-inset-top, 0px) + 1rem)'}}>
          <Header onMenuClick={() => setSidebarOpen(true)} />
        </div>

        {/* Page content with proper spacing for mobile */}
        <main className="flex-1 overflow-y-auto px-4 pt-2" style={{paddingBottom: 'calc(env(safe-area-inset-bottom, 0px) + 5rem)'}}>
          <div className="max-w-7xl mx-auto">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/purchases" element={<Purchases />} />
              <Route path="/usage" element={<Usage />} />
              <Route path="/history" element={<History />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </div>
        </main>
      </div>

      {/* Mobile Footer Navigation with proper safe area padding */}
      <div className="fixed bottom-0 left-0 right-0 z-50" style={{paddingBottom: 'env(safe-area-inset-bottom, 0px)'}}>
        <MobileFooterNav />
      </div>

      {/* Sidebar overlay for mobile */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}

export default Layout
