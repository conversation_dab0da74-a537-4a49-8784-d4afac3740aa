import React, { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import Logo from './Logo'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function InitialSetup() {
  const [initialUnits, setInitialUnits] = useState('')
  const [unitCost, setUnitCost] = useState('')
  const [error, setError] = useState('')
  const { initializeApp, state } = useApp()
  const { theme } = useTheme()

  const handleSubmit = (e) => {
    e.preventDefault()
    setError('')

    const units = parseFloat(initialUnits)
    const cost = parseFloat(unitCost)

    if (isNaN(units) || units < 0) {
      setError('Please enter a valid number of units (0 or greater)')
      return
    }

    if (isNaN(cost) || cost <= 0) {
      setError('Please enter a valid cost per unit (greater than 0)')
      return
    }

    initializeApp(units, cost)
  }

  return (
    <div className={`min-h-screen flex items-center justify-center px-4 py-8 pt-safe-top pb-safe-bottom ${theme.background}`}>
      <div className={`max-w-lg w-full ${theme.card} rounded-2xl shadow-2xl p-8 border ${theme.border}`}>
        {/* Logo and welcome */}
        <div className="text-center mb-8">
          {/* Your Custom Logo */}
          <div className="flex justify-center mb-6">
            <div className="h-24 w-24 flex items-center justify-center">
              <img
                src="/oie_transparent (1).png"
                alt="Prepaid User Electricity Logo"
                className="h-24 w-24 object-contain"
                onError={(e) => {
                  // Fallback to lightning bolt if your logo fails to load
                  e.target.style.display = 'none';
                  e.target.nextElementSibling.style.display = 'flex';
                }}
              />
              <div
                className="h-24 w-24 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white"
                style={{ display: 'none' }}
              >
                <span className="text-white text-4xl font-bold">⚡</span>
              </div>
            </div>
          </div>
          <h1 className={`text-3xl font-bold ${theme.text} mb-4`}>
            Welcome to Prepaid Meter App
          </h1>
          <p className={`${theme.textSecondary} text-lg mb-2`}>
            Let's get started by setting up your initial meter reading and cost settings
          </p>
        </div>

        {/* Setup form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="initialUnits"
              className={`block text-sm font-medium ${theme.text} mb-2`}
            >
              Initial Unit Value
            </label>
            <input
              type="number"
              id="initialUnits"
              value={initialUnits}
              onChange={(e) => setInitialUnits(e.target.value)}
              onWheel={preventNumberInputScroll}
              step="0.01"
              min="0"
              placeholder="Enter your current meter reading"
              className={`w-full px-4 py-4 border-2 ${theme.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${theme.card} ${theme.text} text-lg transition-all duration-200 hover:border-blue-300`}
              required
            />
          </div>

          <div>
            <label
              htmlFor="unitCost"
              className={`block text-sm font-medium ${theme.text} mb-2`}
            >
              Cost per Unit ({state.currencySymbol || 'R'})
            </label>
            <input
              type="number"
              id="unitCost"
              value={unitCost}
              onChange={(e) => setUnitCost(e.target.value)}
              onWheel={preventNumberInputScroll}
              step="0.01"
              min="0.01"
              placeholder="Enter cost per unit (e.g., 2.50)"
              className={`w-full px-4 py-4 border-2 ${theme.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${theme.card} ${theme.text} text-lg transition-all duration-200 hover:border-blue-300`}
              required
            />
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          <div className={`p-4 ${theme.secondary} rounded-lg`}>
            <h3 className={`font-medium ${theme.text} mb-2`}>What happens next?</h3>
            <ul className={`text-sm ${theme.textSecondary} space-y-1`}>
              <li>• This will be your starting point for tracking usage</li>
              <li>• The cost setting will be used for purchase calculations</li>
              <li>• You can add purchases to increase your units</li>
              <li>• Track your daily electricity consumption</li>
              <li>• Set up warnings and monthly resets</li>
            </ul>
          </div>

          <button
            type="submit"
            className={`w-full bg-gradient-to-r ${theme.gradient} text-white py-4 px-6 rounded-xl font-semibold text-lg hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`}
          >
            🚀 Initialize App
          </button>
        </form>

        {/* Additional info */}
        <div className="mt-6 text-center">
          <p className={`text-xs ${theme.textSecondary}`}>
            You can always change these values later in Settings
          </p>
        </div>
      </div>
    </div>
  )
}

export default InitialSetup
